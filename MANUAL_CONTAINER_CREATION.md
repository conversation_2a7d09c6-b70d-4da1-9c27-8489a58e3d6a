# 🐳 Manual Container Creation Guide

## 🚀 **Quick Container Creation**

Since the terminal has issues, here are the exact commands to run manually:

### **Step 1: Make Scripts Executable**
```bash
chmod +x create-containers.sh
chmod +x complete-setup.sh
find scripts/ -name "*.sh" -exec chmod +x {} \;
```

### **Step 2: Create Containers (Option A - Automated)**
```bash
./create-containers.sh
```

### **Step 2: Create Containers (Option B - Manual)**
```bash
# Pull latest images
docker-compose pull

# Start all containers
docker-compose up -d

# Check status
docker-compose ps
```

### **Step 3: Wait for Services**
```bash
# Wait for Minecraft server (check logs)
docker-compose logs -f mc
# Wait until you see "Done" or "Timings Reset", then Ctrl+C

# Test server responsiveness
docker exec minecraft-server-docker-mc-1 rcon-cli "list"

# Test database
docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021"
```

### **Step 4: Run All Enhancement Scripts**
```bash
# Option A: Run everything at once
./complete-setup.sh

# Option B: Run individual scripts
./scripts/fix-skywars-system.sh
./scripts/error-handling-system.sh
./scripts/enhanced-database-operations.sh
./scripts/plugin-integration.sh
./scripts/fallback-systems.sh
./scripts/performance-monitoring.sh
./scripts/command-registration.sh
./scripts/recovery-procedures.sh
./deploy-enhanced-skywars.sh
```

## 📋 **Expected Container Status**

After successful creation, you should see:
```
NAME                           STATUS
minecraft-server-docker-mc-1   Up
minecraft-server-docker-db-1   Up
minecraft-server-docker-fm-1   Up
```

## 🔧 **Service Endpoints**

- **Minecraft Server**: `localhost:25565`
- **File Manager**: `http://localhost:8080`
- **Database**: `localhost:3306`
- **RCON**: `localhost:25575`

## ✅ **Verification Steps**

### **1. Check Containers**
```bash
docker-compose ps
```

### **2. Check Server Logs**
```bash
docker-compose logs mc | tail -20
```

### **3. Test Server Commands**
```bash
docker exec minecraft-server-docker-mc-1 rcon-cli "list"
docker exec minecraft-server-docker-mc-1 rcon-cli "tps"
```

### **4. Test Database**
```bash
docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021"
```

## 🚨 **Troubleshooting**

### **If Containers Don't Start:**
```bash
# Check Docker daemon
docker info

# Check logs
docker-compose logs

# Restart containers
docker-compose down
docker-compose up -d
```

### **If Server Takes Too Long:**
```bash
# Check server logs
docker-compose logs mc

# Check memory usage
docker stats minecraft-server-docker-mc-1

# Restart just the server
docker-compose restart mc
```

### **If Database Issues:**
```bash
# Check database logs
docker-compose logs db

# Restart database
docker-compose restart db

# Test connection
docker exec minecraft-server-docker-db-1 mysql -u hamza -p"Hh@#2021" -e "SELECT 1;"
```

## 📊 **Performance Check**

After containers are running:
```bash
# Check TPS
docker exec minecraft-server-docker-mc-1 rcon-cli "tps"

# Check memory
docker stats --no-stream

# Check disk space
df -h
```

## 🎮 **Ready for Enhancements**

Once containers are running and responsive:

1. **Run enhancement scripts** (see Step 4 above)
2. **Connect to server**: `localhost:25565`
3. **Test basic commands**: `/help`, `/list`
4. **Test SkyWars**: `/sw help` (after enhancements)

## 📝 **Container Creation Checklist**

- [ ] Docker and Docker Compose are working
- [ ] `docker-compose.yml` exists
- [ ] Images pulled successfully
- [ ] All containers show "Up" status
- [ ] Minecraft server logs show "Done" or "Timings Reset"
- [ ] Database responds to ping
- [ ] Server responds to RCON commands
- [ ] Ready to run enhancement scripts

## 🏆 **Success Indicators**

✅ **Containers Created Successfully When:**
- All 3 containers show "Up" status
- Minecraft server logs show completion
- Database accepts connections
- Server responds to `/list` command
- File manager accessible at localhost:8080

## 🚀 **Next Steps After Container Creation**

1. **Run enhancement scripts** to apply all SkyWars improvements
2. **Set up admin permissions** for yourself
3. **Test SkyWars functionality** with `/sw` commands
4. **Monitor performance** with built-in tools
5. **Enjoy your professional SkyWars server!**

---

**Quick Reference:**
- **Create**: `./create-containers.sh` or `docker-compose up -d`
- **Status**: `docker-compose ps`
- **Logs**: `docker-compose logs -f mc`
- **Test**: `docker exec minecraft-server-docker-mc-1 rcon-cli "list"`
