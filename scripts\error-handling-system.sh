#!/bin/bash

# 🛡️ SkyWars Error Handling and Recovery System
# Comprehensive error handling and fallback mechanisms
# Version: 2.1 - Enhanced Error Handling

set -e

echo "🛡️ Setting up SkyWars Error Handling System..."
echo "==============================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_fix() { echo -e "${PURPLE}🛡️ $1${NC}"; }

# Global error handling
set -eE  # Exit on error and inherit ERR trap
trap 'error_handler $? $LINENO $BASH_LINENO "$BASH_COMMAND" $(printf "%s " "${FUNCNAME[@]}")' ERR

error_handler() {
    local exit_code=$1
    local line_no=$2
    local bash_line_no=$3
    local last_command=$4
    local func_stack=$5
    
    log_error "Error occurred in script!"
    echo "Exit Code: $exit_code"
    echo "Line: $line_no"
    echo "Command: $last_command"
    echo "Function Stack: $func_stack"
    echo ""
    
    # Attempt recovery
    log_info "Attempting automatic recovery..."
    attempt_recovery "$exit_code" "$last_command"
}

# Recovery function
attempt_recovery() {
    local exit_code=$1
    local failed_command=$2
    
    case $exit_code in
        1)
            log_warning "General error detected, checking system status..."
            check_system_health
            ;;
        2)
            log_warning "Command not found or permission error..."
            fix_permissions
            ;;
        126)
            log_warning "Permission denied, fixing permissions..."
            fix_permissions
            ;;
        127)
            log_warning "Command not found, checking dependencies..."
            check_dependencies
            ;;
        *)
            log_warning "Unknown error code: $exit_code"
            ;;
    esac
}

# System health check function
check_system_health() {
    log_fix "Performing system health check..."
    
    # Check Docker containers
    if ! docker ps | grep -q minecraft-server-docker-mc-1; then
        log_warning "Minecraft server not running, attempting to start..."
        docker-compose up -d mc || {
            log_error "Failed to start Minecraft server"
            return 1
        }
    fi
    
    if ! docker ps | grep -q minecraft-server-docker-db-1; then
        log_warning "Database not running, attempting to start..."
        docker-compose up -d db || {
            log_error "Failed to start database"
            return 1
        }
    fi
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 90 ]]; then
        log_warning "Disk usage is high: ${disk_usage}%"
        cleanup_old_files
    fi
    
    # Check memory usage
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [[ $memory_usage -gt 90 ]]; then
        log_warning "Memory usage is high: ${memory_usage}%"
        restart_services
    fi
    
    log_success "System health check completed"
}

# Fix permissions function
fix_permissions() {
    log_fix "Fixing file permissions..."
    
    # Fix script permissions
    find scripts/ -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
    
    # Fix plugin directory permissions
    chmod -R 755 plugins/ 2>/dev/null || true
    
    # Fix world directory permissions
    chmod -R 755 modern_skywar/ 2>/dev/null || true
    
    log_success "Permissions fixed"
}

# Check dependencies function
check_dependencies() {
    log_fix "Checking and installing dependencies..."
    
    # Check for required commands
    local required_commands=("docker" "docker-compose" "curl" "wget")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_warning "$cmd not found, attempting to install..."
            
            case "$cmd" in
                "docker")
                    log_error "Docker is required but not installed. Please install Docker first."
                    ;;
                "docker-compose")
                    log_error "Docker Compose is required but not installed. Please install Docker Compose first."
                    ;;
                "curl"|"wget")
                    log_warning "$cmd not available, trying alternative download methods..."
                    ;;
            esac
        else
            log_success "$cmd is available"
        fi
    done
}

# Cleanup old files function
cleanup_old_files() {
    log_fix "Cleaning up old files to free disk space..."
    
    # Clean old logs
    find . -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # Clean old backups
    find backups/ -mtime +30 -delete 2>/dev/null || true
    
    # Clean Docker images
    docker image prune -f 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Restart services function
restart_services() {
    log_fix "Restarting services to free memory..."
    
    # Restart Minecraft server
    docker-compose restart mc || {
        log_error "Failed to restart Minecraft server"
        return 1
    }
    
    # Wait for server to come back up
    for i in {1..60}; do
        if docker-compose logs mc | tail -10 | grep -q "Done\|Timings Reset"; then
            break
        fi
        sleep 2
    done
    
    log_success "Services restarted"
}

# Create error handling configuration
create_error_config() {
    log_fix "Creating error handling configuration..."
    
    mkdir -p config/error-handling
    
    cat > config/error-handling/error-config.yml << 'EOF'
# SkyWars Error Handling Configuration
error-handling:
  # Global settings
  enabled: true
  log-errors: true
  auto-recovery: true
  
  # Recovery settings
  recovery:
    max-attempts: 3
    retry-delay: 5  # seconds
    escalate-failures: true
    
  # Monitoring
  monitoring:
    check-interval: 60  # seconds
    health-checks: true
    performance-monitoring: true
    
  # Notifications
  notifications:
    console: true
    file: true
    discord: false  # Configure webhook if needed
    
  # Fallback mechanisms
  fallbacks:
    use-basic-config: true
    disable-advanced-features: true
    safe-mode: true
EOF

    log_success "Error handling configuration created"
}

# Create recovery procedures
create_recovery_procedures() {
    log_fix "Creating automated recovery procedures..."
    
    mkdir -p scripts/recovery
    
    # Database recovery script
    cat > scripts/recovery/database-recovery.sh << 'EOF'
#!/bin/bash
# Database Recovery Script

echo "🔧 Database Recovery Procedure"
echo "=============================="

# Check database connection
if ! docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
    echo "❌ Database connection failed"
    
    # Restart database
    echo "🔄 Restarting database..."
    docker-compose restart db
    
    # Wait for database to start
    for i in {1..30}; do
        if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
            echo "✅ Database recovered"
            exit 0
        fi
        sleep 2
    done
    
    echo "❌ Database recovery failed"
    exit 1
else
    echo "✅ Database is healthy"
fi
EOF

    # Server recovery script
    cat > scripts/recovery/server-recovery.sh << 'EOF'
#!/bin/bash
# Server Recovery Script

echo "🔧 Server Recovery Procedure"
echo "============================"

# Check server status
if ! docker ps | grep -q minecraft-server-docker-mc-1; then
    echo "❌ Server is not running"
    
    # Start server
    echo "🔄 Starting server..."
    docker-compose up -d mc
    
    # Wait for server to start
    for i in {1..60}; do
        if docker-compose logs mc | tail -10 | grep -q "Done\|Timings Reset"; then
            echo "✅ Server recovered"
            exit 0
        fi
        sleep 3
    done
    
    echo "❌ Server recovery failed"
    exit 1
else
    echo "✅ Server is healthy"
fi
EOF

    # Configuration recovery script
    cat > scripts/recovery/config-recovery.sh << 'EOF'
#!/bin/bash
# Configuration Recovery Script

echo "🔧 Configuration Recovery Procedure"
echo "==================================="

# Check for critical configuration files
critical_configs=(
    "plugins/SkyWars/config.yml"
    "plugins/SkyWars/arenas.yml"
)

for config in "${critical_configs[@]}"; do
    if [[ ! -f "$config" ]]; then
        echo "❌ Missing critical config: $config"
        
        # Restore from backup if available
        if [[ -f "$config.backup" ]]; then
            echo "🔄 Restoring from backup..."
            cp "$config.backup" "$config"
            echo "✅ Restored $config"
        else
            echo "⚠️  No backup found, creating minimal config..."
            # Create minimal working configuration
            mkdir -p "$(dirname "$config")"
            echo "# Minimal SkyWars Configuration" > "$config"
            echo "enabled: true" >> "$config"
            echo "✅ Created minimal $config"
        fi
    else
        echo "✅ $config exists"
    fi
done
EOF

    # Make recovery scripts executable
    chmod +x scripts/recovery/*.sh
    
    log_success "Recovery procedures created"
}

# Create monitoring system
create_monitoring_system() {
    log_fix "Creating monitoring system..."
    
    cat > scripts/continuous-monitor.sh << 'EOF'
#!/bin/bash
# Continuous SkyWars System Monitor

echo "🔍 Starting SkyWars Continuous Monitor..."

while true; do
    # Check system health every minute
    sleep 60
    
    # Check server status
    if ! docker ps | grep -q minecraft-server-docker-mc-1; then
        echo "$(date): ❌ Server down, attempting recovery..."
        ./scripts/recovery/server-recovery.sh
    fi
    
    # Check database status
    if ! docker ps | grep -q minecraft-server-docker-db-1; then
        echo "$(date): ❌ Database down, attempting recovery..."
        ./scripts/recovery/database-recovery.sh
    fi
    
    # Check TPS
    TPS=$(docker exec minecraft-server-docker-mc-1 rcon-cli "tps" 2>/dev/null | grep -o '[0-9]*\.[0-9]*' | head -1 || echo "0")
    if (( $(echo "$TPS < 18.0" | bc -l) )); then
        echo "$(date): ⚠️  Low TPS detected: $TPS"
    fi
    
    # Check memory usage
    MEMORY_PERCENT=$(docker stats minecraft-server-docker-mc-1 --no-stream --format "{{.MemPerc}}" 2>/dev/null | sed 's/%//' || echo "0")
    if (( $(echo "$MEMORY_PERCENT > 90" | bc -l) )); then
        echo "$(date): ⚠️  High memory usage: ${MEMORY_PERCENT}%"
        # Restart server if memory usage is too high
        docker-compose restart mc
    fi
done
EOF

    chmod +x scripts/continuous-monitor.sh
    
    log_success "Monitoring system created"
}

# Create validation system
create_validation_system() {
    log_fix "Creating validation system..."
    
    cat > scripts/validate-system.sh << 'EOF'
#!/bin/bash
# System Validation Script

echo "🔍 SkyWars System Validation"
echo "============================"

ERRORS=0

# Validate Docker containers
echo "Checking Docker containers..."
if ! docker ps | grep -q minecraft-server-docker-mc-1; then
    echo "❌ Minecraft server container not running"
    ((ERRORS++))
else
    echo "✅ Minecraft server container running"
fi

if ! docker ps | grep -q minecraft-server-docker-db-1; then
    echo "❌ Database container not running"
    ((ERRORS++))
else
    echo "✅ Database container running"
fi

# Validate configuration files
echo "Checking configuration files..."
if [[ -f "plugins/SkyWars/config.yml" ]]; then
    echo "✅ Main configuration exists"
else
    echo "❌ Main configuration missing"
    ((ERRORS++))
fi

# Validate world
echo "Checking world files..."
if [[ -d "modern_skywar" ]]; then
    echo "✅ SkyWars world exists"
else
    echo "❌ SkyWars world missing"
    ((ERRORS++))
fi

# Validate database connection
echo "Checking database connection..."
if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    ((ERRORS++))
fi

# Summary
echo ""
echo "Validation Summary:"
echo "=================="
if [[ $ERRORS -eq 0 ]]; then
    echo "✅ All checks passed! System is healthy."
    exit 0
else
    echo "❌ $ERRORS error(s) found. System needs attention."
    exit 1
fi
EOF

    chmod +x scripts/validate-system.sh
    
    log_success "Validation system created"
}

# Main execution
main() {
    log_info "Setting up comprehensive error handling system..."
    
    # Create directory structure
    mkdir -p {config/error-handling,scripts/recovery,logs}
    
    # Setup error handling components
    create_error_config
    create_recovery_procedures
    create_monitoring_system
    create_validation_system
    
    # Test the system
    log_info "Testing error handling system..."
    if ./scripts/validate-system.sh; then
        log_success "System validation passed"
    else
        log_warning "System validation found issues, but error handling is in place"
    fi
    
    log_success "🛡️ Error handling system setup complete!"
    echo ""
    echo "✅ Error Handling Features:"
    echo "   • Automatic error detection and recovery"
    echo "   • System health monitoring"
    echo "   • Database recovery procedures"
    echo "   • Server recovery procedures"
    echo "   • Configuration recovery"
    echo "   • Continuous monitoring"
    echo ""
    echo "🔧 Available Tools:"
    echo "   • ./scripts/validate-system.sh - System validation"
    echo "   • ./scripts/continuous-monitor.sh - Continuous monitoring"
    echo "   • ./scripts/recovery/database-recovery.sh - Database recovery"
    echo "   • ./scripts/recovery/server-recovery.sh - Server recovery"
    echo "   • ./scripts/recovery/config-recovery.sh - Config recovery"
    echo ""
    echo "📋 Recommendations:"
    echo "   1. Run validation script regularly"
    echo "   2. Monitor system logs"
    echo "   3. Keep backups updated"
    echo "   4. Test recovery procedures"
    echo ""
    log_success "Error handling system is ready! 🛡️"
}

# Run main function
main "$@"
