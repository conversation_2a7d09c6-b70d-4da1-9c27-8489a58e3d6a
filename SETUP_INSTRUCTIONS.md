# 🚀 Complete SkyWars Setup Instructions

## 🎯 **Quick Start - Run Everything**

Since you've already deleted the containers, here's how to recreate everything and apply all enhancements:

### **Step 1: Make Scripts Executable**
```bash
# Make the main setup script executable
chmod +x complete-setup.sh

# Make all enhancement scripts executable
chmod +x deploy-enhanced-skywars.sh
find scripts/ -name "*.sh" -exec chmod +x {} \;
```

### **Step 2: Run Complete Setup**
```bash
# Run the complete setup (this does everything)
./complete-setup.sh
```

## 📋 **What the Setup Script Does**

### **Phase 1: Prerequisites Check**
- ✅ Verifies Docker and Docker Compose are available
- ✅ Checks for docker-compose.yml
- ✅ Creates necessary directory structure

### **Phase 2: Script Preparation**
- ✅ Makes all scripts executable
- ✅ Verifies enhancement scripts are available

### **Phase 3: Container Creation**
- ✅ Pulls latest Docker images
- ✅ Starts all containers (Minecraft + Database + File Manager)
- ✅ Verifies containers are running

### **Phase 4: Service Readiness**
- ✅ Waits for Minecraft server to fully start
- ✅ Waits for database to be ready
- ✅ Ensures all services are stable

### **Phase 5: Enhancement Scripts** (The Main Event!)
Runs all enhancement scripts in order:
1. `scripts/fix-skywars-system.sh` - Core system fixes
2. `scripts/error-handling-system.sh` - Error handling setup
3. `scripts/enhanced-database-operations.sh` - Database enhancements
4. `scripts/plugin-integration.sh` - Plugin integration
5. `scripts/fallback-systems.sh` - Fallback mechanisms
6. `scripts/performance-monitoring.sh` - Performance monitoring
7. `scripts/command-registration.sh` - Command system
8. `scripts/recovery-procedures.sh` - Recovery procedures
9. `deploy-enhanced-skywars.sh` - Master deployment

### **Phase 6: Validation**
- ✅ Runs comprehensive system validation
- ✅ Performs health assessment
- ✅ Tests basic functionality

### **Phase 7: Monitoring**
- ✅ Starts continuous performance monitoring
- ✅ Creates performance baseline

### **Phase 8: Permissions**
- ✅ Sets up basic SkyWars permissions
- ✅ Creates admin groups

### **Phase 9: Final Status**
- ✅ Shows container status
- ✅ Displays service endpoints
- ✅ Lists available commands

## ⚡ **Alternative: Manual Step-by-Step**

If you prefer to run things manually or the main script has issues:

### **1. Create Containers**
```bash
# Pull and start containers
docker-compose pull
docker-compose up -d

# Wait for services (check logs)
docker-compose logs -f mc
# Wait until you see "Done" or "Timings Reset"
```

### **2. Run Enhancement Scripts**
```bash
# Make scripts executable
find scripts/ -name "*.sh" -exec chmod +x {} \;
chmod +x *.sh

# Run in order
./scripts/fix-skywars-system.sh
./scripts/error-handling-system.sh
./scripts/enhanced-database-operations.sh
./scripts/plugin-integration.sh
./scripts/fallback-systems.sh
./scripts/performance-monitoring.sh
./scripts/command-registration.sh
./scripts/recovery-procedures.sh

# Run master deployment
./deploy-enhanced-skywars.sh
```

### **3. Validate and Monitor**
```bash
# Validate system
./scripts/validation-system.sh

# Check health
./scripts/master-recovery.sh assess

# Start monitoring
./scripts/continuous-performance-monitor.sh start
```

## 🎮 **Testing Your Setup**

### **Basic Connection Test**
1. **Connect to server**: `localhost:25565`
2. **Test basic commands**:
   ```
   /sw help
   /sw join solo
   /sw stats
   /sw leave
   ```

### **Admin Setup**
```bash
# Give yourself admin permissions
docker exec minecraft-server-docker-mc-1 rcon-cli "lp user YourUsername parent add skywars_admin"

# Test admin commands
/sw admin
/sw arena list
/sw reload
```

### **Performance Check**
```bash
# Check system performance
./scripts/system-performance-monitor.sh

# Check monitoring status
./scripts/continuous-performance-monitor.sh status

# View logs
docker-compose logs -f mc
tail -f logs/performance-daemon.log
```

## 🔧 **Service Endpoints**

After setup completion:
- **Minecraft Server**: `localhost:25565`
- **File Manager**: `http://localhost:8080`
- **Database**: `localhost:3306`
- **RCON**: `localhost:25575`

## 📊 **Expected Results**

### **Successful Setup Indicators:**
- ✅ All containers show "Up" status
- ✅ Minecraft server logs show "Done" or "Timings Reset"
- ✅ Database responds to ping
- ✅ SkyWars commands work in-game (`/sw help`)
- ✅ Performance monitoring is active
- ✅ Health checks pass

### **Enhanced Features Available:**
- ✅ Professional SkyWars match management
- ✅ Advanced queue system with smart matchmaking
- ✅ Comprehensive statistics and leaderboards
- ✅ Professional spectator system
- ✅ Real-time performance monitoring
- ✅ Automatic error handling and recovery
- ✅ Database optimization and management
- ✅ Plugin integration layer
- ✅ Administrative tools and commands

## 🚨 **Troubleshooting**

### **If Containers Don't Start:**
```bash
# Check Docker status
docker info

# Check logs
docker-compose logs

# Try restart
docker-compose down
docker-compose up -d
```

### **If Scripts Fail:**
```bash
# Check script permissions
ls -la scripts/

# Make executable manually
chmod +x scripts/*.sh

# Run individual scripts to identify issues
./scripts/fix-skywars-system.sh
```

### **If SkyWars Doesn't Work:**
```bash
# Check plugin status
docker exec minecraft-server-docker-mc-1 rcon-cli "plugins"

# Run recovery
./scripts/master-recovery.sh recover all

# Check validation
./scripts/validation-system.sh
```

## ✅ **Success Checklist**

- [ ] Containers are running (`docker-compose ps`)
- [ ] Server responds (`docker exec minecraft-server-docker-mc-1 rcon-cli "list"`)
- [ ] Database is accessible
- [ ] SkyWars commands work (`/sw help`)
- [ ] Performance monitoring is active
- [ ] Health checks pass
- [ ] Admin permissions work

## 🏆 **You're Done!**

Once the setup completes successfully, you'll have:
- **Professional SkyWars server** with all 22 enhancements
- **Real-time monitoring** and alerting
- **Automatic error recovery** systems
- **Optimized database** performance
- **Complete admin toolkit**

**Ready for epic SkyWars battles!** 🎮⚔️🏆

---

**Quick Command Reference:**
- **Setup**: `./complete-setup.sh`
- **Health Check**: `./scripts/master-recovery.sh assess`
- **Performance**: `./scripts/system-performance-monitor.sh`
- **Logs**: `docker-compose logs -f mc`
