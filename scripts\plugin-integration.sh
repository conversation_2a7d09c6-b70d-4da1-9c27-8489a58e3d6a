#!/bin/bash

# 🔌 SkyWars Plugin Integration Layer
# Ensures proper integration with existing plugins
# Version: 2.1 - Enhanced Integration

set -e

echo "🔌 Setting up SkyWars Plugin Integration..."
echo "==========================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_integrate() { echo -e "${PURPLE}🔌 $1${NC}"; }

# Function to check plugin compatibility
check_plugin_compatibility() {
    log_integrate "Checking plugin compatibility..."
    
    # Check for existing plugins
    local plugins_found=()
    local plugins_missing=()
    
    # Required plugins
    local required_plugins=(
        "AuthMe"
        "Essentials"
        "Multiverse-Core"
    )
    
    # Optional plugins
    local optional_plugins=(
        "Vault"
        "WorldEdit"
        "LuckPerms"
        "PlaceholderAPI"
    )
    
    # Check required plugins
    for plugin in "${required_plugins[@]}"; do
        if [[ -d "plugins/$plugin" ]]; then
            plugins_found+=("$plugin")
            log_success "Required plugin found: $plugin"
        else
            plugins_missing+=("$plugin")
            log_error "Required plugin missing: $plugin"
        fi
    done
    
    # Check optional plugins
    for plugin in "${optional_plugins[@]}"; do
        if [[ -d "plugins/$plugin" ]] || [[ -f "plugins/$plugin.jar" ]]; then
            plugins_found+=("$plugin")
            log_success "Optional plugin found: $plugin"
        else
            log_warning "Optional plugin not found: $plugin"
        fi
    done
    
    # Download missing required plugins
    if [[ ${#plugins_missing[@]} -gt 0 ]]; then
        log_warning "Downloading missing required plugins..."
        download_missing_plugins "${plugins_missing[@]}"
    fi
}

# Function to download missing plugins
download_missing_plugins() {
    local missing_plugins=("$@")
    
    for plugin in "${missing_plugins[@]}"; do
        log_info "Downloading $plugin..."
        
        case "$plugin" in
            "Vault")
                curl -L -o "plugins/Vault.jar" "https://github.com/MilkBowl/Vault/releases/latest/download/Vault.jar" 2>/dev/null || {
                    log_warning "Failed to download Vault"
                }
                ;;
            "WorldEdit")
                log_info "WorldEdit download requires manual installation from dev.bukkit.org"
                ;;
            "LuckPerms")
                curl -L -o "plugins/LuckPerms.jar" "https://download.luckperms.net/1515/bukkit/loader/LuckPerms-Bukkit-5.4.102.jar" 2>/dev/null || {
                    log_warning "Failed to download LuckPerms"
                }
                ;;
            "PlaceholderAPI")
                log_info "PlaceholderAPI download requires manual installation from spigotmc.org"
                ;;
            *)
                log_warning "Unknown plugin: $plugin"
                ;;
        esac
    done
}

# Function to create AuthMe integration
create_authme_integration() {
    log_integrate "Creating AuthMe integration..."
    
    # Check if AuthMe is configured
    if [[ -f "plugins/AuthMe/config.yml" ]]; then
        log_success "AuthMe configuration found"
        
        # Create AuthMe integration script
        cat > scripts/authme-integration.sh << 'EOF'
#!/bin/bash
# AuthMe Integration for SkyWars

# Function to check if player is authenticated
check_player_auth() {
    local player="$1"
    
    # Check if player is logged in via AuthMe
    local auth_status=$(docker exec minecraft-server-docker-mc-1 rcon-cli "authme islogged $player" 2>/dev/null || echo "false")
    
    if echo "$auth_status" | grep -q "true\|logged"; then
        echo "true"
    else
        echo "false"
    fi
}

# Function to require authentication before SkyWars
require_auth_for_skywars() {
    local player="$1"
    
    if [[ "$(check_player_auth "$player")" == "false" ]]; then
        docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Please login first with /login <password>\",\"color\":\"red\"}"
        return 1
    fi
    
    return 0
}

# Export functions for use in other scripts
export -f check_player_auth
export -f require_auth_for_skywars
EOF
        
        chmod +x scripts/authme-integration.sh
        log_success "AuthMe integration created"
    else
        log_warning "AuthMe configuration not found"
    fi
}

# Function to create Essentials integration
create_essentials_integration() {
    log_integrate "Creating Essentials integration..."
    
    if [[ -f "plugins/Essentials/config.yml" ]]; then
        log_success "Essentials configuration found"
        
        # Create Essentials integration script
        cat > scripts/essentials-integration.sh << 'EOF'
#!/bin/bash
# Essentials Integration for SkyWars

# Function to check player balance
check_player_balance() {
    local player="$1"
    
    local balance=$(docker exec minecraft-server-docker-mc-1 rcon-cli "money $player" 2>/dev/null | grep -o '[0-9]*\.[0-9]*' || echo "0")
    echo "$balance"
}

# Function to give money to player
give_money() {
    local player="$1"
    local amount="$2"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "eco give $player $amount" 2>/dev/null
}

# Function to take money from player
take_money() {
    local player="$1"
    local amount="$2"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "eco take $player $amount" 2>/dev/null
}

# Function to teleport player to lobby
teleport_to_lobby() {
    local player="$1"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "spawn $player" 2>/dev/null
}

# Export functions
export -f check_player_balance
export -f give_money
export -f take_money
export -f teleport_to_lobby
EOF
        
        chmod +x scripts/essentials-integration.sh
        log_success "Essentials integration created"
    else
        log_warning "Essentials configuration not found"
    fi
}

# Function to create Multiverse integration
create_multiverse_integration() {
    log_integrate "Creating Multiverse integration..."
    
    if [[ -f "plugins/Multiverse-Core/config.yml" ]]; then
        log_success "Multiverse configuration found"
        
        # Create Multiverse integration script
        cat > scripts/multiverse-integration.sh << 'EOF'
#!/bin/bash
# Multiverse Integration for SkyWars

# Function to create SkyWars world if it doesn't exist
create_skywars_world() {
    local world_name="$1"
    
    # Check if world exists in Multiverse
    local world_exists=$(docker exec minecraft-server-docker-mc-1 rcon-cli "mv list" 2>/dev/null | grep "$world_name" || echo "")
    
    if [[ -z "$world_exists" ]]; then
        log_info "Creating world $world_name in Multiverse..."
        docker exec minecraft-server-docker-mc-1 rcon-cli "mv create $world_name normal" 2>/dev/null
        docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set gamemode creative $world_name" 2>/dev/null
        docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set pvp true $world_name" 2>/dev/null
        log_success "World $world_name created"
    else
        log_success "World $world_name already exists"
    fi
}

# Function to teleport player to world
teleport_to_world() {
    local player="$1"
    local world="$2"
    local x="${3:-0}"
    local y="${4:-70}"
    local z="${5:-0}"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv tp $player $world" 2>/dev/null
}

# Function to set world properties for SkyWars
configure_skywars_world() {
    local world_name="$1"
    
    # Set world properties
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set gamemode survival $world_name" 2>/dev/null
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set pvp true $world_name" 2>/dev/null
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set difficulty normal $world_name" 2>/dev/null
    docker exec minecraft-server-docker-mc-1 rcon-cli "mv modify set allowFlight false $world_name" 2>/dev/null
    
    log_success "World $world_name configured for SkyWars"
}

# Export functions
export -f create_skywars_world
export -f teleport_to_world
export -f configure_skywars_world
EOF
        
        chmod +x scripts/multiverse-integration.sh
        log_success "Multiverse integration created"
    else
        log_warning "Multiverse configuration not found"
    fi
}

# Function to create unified SkyWars command system
create_unified_command_system() {
    log_integrate "Creating unified command system..."
    
    cat > scripts/skywars-unified-commands.sh << 'EOF'
#!/bin/bash
# Unified SkyWars Command System
# Integrates with all existing plugins

# Source integration scripts
source scripts/authme-integration.sh 2>/dev/null || true
source scripts/essentials-integration.sh 2>/dev/null || true
source scripts/multiverse-integration.sh 2>/dev/null || true

# Main SkyWars command handler
handle_skywars_command() {
    local player="$1"
    local command="$2"
    local args="${@:3}"
    
    # Check authentication first (if AuthMe is available)
    if command -v require_auth_for_skywars &> /dev/null; then
        if ! require_auth_for_skywars "$player"; then
            return 1
        fi
    fi
    
    case "$command" in
        "join")
            handle_join_command "$player" "$args"
            ;;
        "leave")
            handle_leave_command "$player"
            ;;
        "stats")
            handle_stats_command "$player" "$args"
            ;;
        "spectate")
            handle_spectate_command "$player" "$args"
            ;;
        "help")
            handle_help_command "$player"
            ;;
        *)
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Unknown SkyWars command. Use /sw help\",\"color\":\"red\"}"
            ;;
    esac
}

# Join command handler
handle_join_command() {
    local player="$1"
    local mode="${2:-solo}"
    
    # Check if player is in lobby world
    local current_world=$(docker exec minecraft-server-docker-mc-1 rcon-cli "data get entity $player Dimension" 2>/dev/null | grep -o '[^:]*$' || echo "unknown")
    
    if [[ "$current_world" != "lobby" ]]; then
        # Teleport to lobby first
        if command -v teleport_to_lobby &> /dev/null; then
            teleport_to_lobby "$player"
        fi
    fi
    
    # Create SkyWars world if needed
    if command -v create_skywars_world &> /dev/null; then
        create_skywars_world "modern_skywar"
        configure_skywars_world "modern_skywar"
    fi
    
    # Join queue (basic implementation)
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Joining $mode SkyWars queue...\",\"color\":\"green\"}"
    
    # Teleport to SkyWars world
    if command -v teleport_to_world &> /dev/null; then
        teleport_to_world "$player" "modern_skywar" 0 70 0
    fi
    
    # Give starting items
    docker exec minecraft-server-docker-mc-1 rcon-cli "clear $player"
    docker exec minecraft-server-docker-mc-1 rcon-cli "give $player stone_sword 1"
    docker exec minecraft-server-docker-mc-1 rcon-cli "give $player bread 5"
    docker exec minecraft-server-docker-mc-1 rcon-cli "give $player oak_planks 16"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Welcome to SkyWars! Good luck!\",\"color\":\"gold\"}"
}

# Leave command handler
handle_leave_command() {
    local player="$1"
    
    # Teleport back to lobby
    if command -v teleport_to_lobby &> /dev/null; then
        teleport_to_lobby "$player"
    else
        docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player 0 70 0"
    fi
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Left SkyWars and returned to lobby\",\"color\":\"yellow\"}"
}

# Stats command handler
handle_stats_command() {
    local player="$1"
    local target="${2:-$player}"
    
    # Get player balance if Essentials is available
    local balance="Unknown"
    if command -v check_player_balance &> /dev/null; then
        balance=$(check_player_balance "$target")
    fi
    
    # Display basic stats (would be enhanced with database integration)
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"=== SkyWars Stats for $target ===\",\"color\":\"gold\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Wins: 0 | Losses: 0 | Kills: 0\",\"color\":\"aqua\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Balance: $balance coins\",\"color\":\"green\"}"
}

# Spectate command handler
handle_spectate_command() {
    local player="$1"
    
    # Teleport to SkyWars world in spectator mode
    if command -v teleport_to_world &> /dev/null; then
        teleport_to_world "$player" "modern_skywar" 0 80 0
    fi
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "gamemode spectator $player"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"You are now spectating SkyWars!\",\"color\":\"gray\"}"
}

# Help command handler
handle_help_command() {
    local player="$1"
    
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"=== SkyWars Commands ===\",\"color\":\"gold\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw join [solo|doubles] - Join a game\",\"color\":\"yellow\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw leave - Leave current game\",\"color\":\"yellow\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw stats [player] - View statistics\",\"color\":\"yellow\"}"
    docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"/sw spectate - Spectate games\",\"color\":\"yellow\"}"
}

# Command aliases
alias sw='handle_skywars_command'
alias skywars='handle_skywars_command'

# Export main function
export -f handle_skywars_command
EOF
        
        chmod +x scripts/skywars-unified-commands.sh
        log_success "Unified command system created"
}

# Function to create plugin dependency checker
create_dependency_checker() {
    log_integrate "Creating plugin dependency checker..."
    
    cat > scripts/check-plugin-dependencies.sh << 'EOF'
#!/bin/bash
# Plugin Dependency Checker

echo "🔍 Checking Plugin Dependencies..."
echo "=================================="

# Check server status
if ! docker ps | grep -q minecraft-server-docker-mc-1; then
    echo "❌ Server is not running"
    exit 1
fi

# Get plugin list from server
PLUGINS=$(docker exec minecraft-server-docker-mc-1 rcon-cli "plugins" 2>/dev/null || echo "")

# Check for required plugins
echo "Required Plugins:"
echo "=================="

check_plugin() {
    local plugin_name="$1"
    local required="$2"
    
    if echo "$PLUGINS" | grep -q "$plugin_name"; then
        echo "✅ $plugin_name - Loaded"
        return 0
    else
        if [[ "$required" == "true" ]]; then
            echo "❌ $plugin_name - Missing (Required)"
            return 1
        else
            echo "⚠️  $plugin_name - Missing (Optional)"
            return 0
        fi
    fi
}

ERRORS=0

# Check required plugins
check_plugin "AuthMe" "true" || ((ERRORS++))
check_plugin "Essentials" "true" || ((ERRORS++))
check_plugin "Multiverse-Core" "true" || ((ERRORS++))

echo ""
echo "Optional Plugins:"
echo "================="

# Check optional plugins
check_plugin "Vault" "false"
check_plugin "WorldEdit" "false"
check_plugin "LuckPerms" "false"
check_plugin "PlaceholderAPI" "false"

echo ""
echo "Summary:"
echo "========"

if [[ $ERRORS -eq 0 ]]; then
    echo "✅ All required plugins are loaded"
    exit 0
else
    echo "❌ $ERRORS required plugin(s) missing"
    exit 1
fi
EOF
        
        chmod +x scripts/check-plugin-dependencies.sh
        log_success "Dependency checker created"
}

# Main execution
main() {
    log_info "Setting up plugin integration layer..."
    
    # Check compatibility
    check_plugin_compatibility
    
    # Create integration scripts
    create_authme_integration
    create_essentials_integration
    create_multiverse_integration
    create_unified_command_system
    create_dependency_checker
    
    # Test integrations
    log_info "Testing plugin integrations..."
    if ./scripts/check-plugin-dependencies.sh; then
        log_success "All required plugins are available"
    else
        log_warning "Some plugins are missing, but integration layer is ready"
    fi
    
    log_success "🔌 Plugin integration layer setup complete!"
    echo ""
    echo "✅ Integration Features:"
    echo "   • AuthMe authentication integration"
    echo "   • Essentials economy integration"
    echo "   • Multiverse world management"
    echo "   • Unified command system"
    echo "   • Plugin dependency checking"
    echo ""
    echo "🎮 Available Commands:"
    echo "   • ./scripts/skywars-unified-commands.sh <player> <command>"
    echo "   • ./scripts/check-plugin-dependencies.sh"
    echo ""
    echo "📋 Integration Status:"
    echo "   • Commands are now plugin-aware"
    echo "   • Authentication is enforced"
    echo "   • Economy integration is active"
    echo "   • World management is automated"
    echo ""
    log_success "Plugin integration is ready! 🔌"
}

# Run main function
main "$@"
