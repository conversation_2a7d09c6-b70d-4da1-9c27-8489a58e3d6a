-- AuthMe Authentication Tables for Minecraft Server
-- Database: minecraft-abusaker
-- This script creates the necessary tables for AuthMe authentication system

USE `minecraft-abusaker`;

-- Create the main AuthMe authentication table
CREATE TABLE IF NOT EXISTS `authme` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(255) NOT NULL,
    `realname` VARCHAR(255) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    `ip` VARCHAR(40) DEFAULT NULL,
    `lastlogin` BIGINT(20) DEFAULT NULL,
    `x` DOUBLE NOT NULL DEFAULT 0,
    `y` DOUBLE NOT NULL DEFAULT 0,
    `z` DOUBLE NOT NULL DEFAULT 0,
    `world` VARCHAR(255) NOT NULL DEFAULT 'world',
    `regdate` BIGINT(20) NOT NULL DEFAULT 0,
    `regip` VARCHAR(40) DEFAULT NULL,
    `yaw` FLOAT DEFAULT NULL,
    `pitch` FLOAT DEFAULT NULL,
    `email` VARCHAR(255) DEFAULT NULL,
    `isLogged` SMALLINT(6) NOT NULL DEFAULT 0,
    `hasSession` SMALLINT(6) NOT NULL DEFAULT 0,
    `totp` VARCHAR(32) DEFAULT NULL,
    `uuid` VARCHAR(36) DEFAULT NULL,
    `salt` VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`),
    KEY `idx_uuid` (`uuid`),
    KEY `idx_ip` (`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create the users table for additional player data and statistics
CREATE TABLE IF NOT EXISTS `users` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(255) NOT NULL,
    `display_name` VARCHAR(255) NOT NULL,
    `uuid` VARCHAR(36) DEFAULT NULL,
    `registration_ip` VARCHAR(40) DEFAULT NULL,
    `first_join_date` DATETIME NOT NULL,
    `last_login` DATETIME DEFAULT NULL,
    `last_login_ip` VARCHAR(40) DEFAULT NULL,
    `total_logins` INT(11) NOT NULL DEFAULT 0,
    `total_playtime` BIGINT(20) NOT NULL DEFAULT 0,
    `is_premium` BOOLEAN NOT NULL DEFAULT FALSE,
    `is_banned` BOOLEAN NOT NULL DEFAULT FALSE,
    `ban_reason` TEXT DEFAULT NULL,
    `ban_date` DATETIME DEFAULT NULL,
    `ban_expires` DATETIME DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`),
    KEY `idx_uuid` (`uuid`),
    KEY `idx_last_login` (`last_login`),
    KEY `idx_is_banned` (`is_banned`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create a table for player sessions (optional, for advanced session management)
CREATE TABLE IF NOT EXISTS `player_sessions` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(255) NOT NULL,
    `session_id` VARCHAR(255) NOT NULL,
    `ip_address` VARCHAR(40) NOT NULL,
    `login_time` DATETIME NOT NULL,
    `last_activity` DATETIME NOT NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `user_agent` TEXT DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `session_id` (`session_id`),
    KEY `idx_username` (`username`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_last_activity` (`last_activity`),
    FOREIGN KEY (`username`) REFERENCES `authme` (`username`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create a table for login attempts and security logging
CREATE TABLE IF NOT EXISTS `login_attempts` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(255) DEFAULT NULL,
    `ip_address` VARCHAR(40) NOT NULL,
    `attempt_time` DATETIME NOT NULL,
    `success` BOOLEAN NOT NULL DEFAULT FALSE,
    `failure_reason` VARCHAR(255) DEFAULT NULL,
    `user_agent` TEXT DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_ip_address` (`ip_address`),
    KEY `idx_attempt_time` (`attempt_time`),
    KEY `idx_success` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some example data (optional - remove if not needed)
-- This shows the structure and can be used for testing

-- Example AuthMe user (password is 'password123' hashed with BCrypt)
INSERT IGNORE INTO `authme` (`username`, `realname`, `password`, `regdate`, `regip`, `world`) VALUES
('testuser', 'TestUser', '$2a$10$example.hash.here.for.password123', UNIX_TIMESTAMP(), '127.0.0.1', 'world');

-- Example user data
INSERT IGNORE INTO `users` (`username`, `display_name`, `registration_ip`, `first_join_date`) VALUES
('testuser', 'TestUser', '127.0.0.1', NOW());

-- Create indexes for better performance (using ALTER TABLE for compatibility)
ALTER TABLE `authme` ADD INDEX `idx_authme_lastlogin` (`lastlogin`);
ALTER TABLE `authme` ADD INDEX `idx_authme_regdate` (`regdate`);
ALTER TABLE `users` ADD INDEX `idx_users_first_join` (`first_join_date`);
ALTER TABLE `users` ADD INDEX `idx_users_total_logins` (`total_logins`);

-- Create SkyWars statistics tables
CREATE TABLE IF NOT EXISTS `skywars_players` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `uuid` VARCHAR(36) NOT NULL,
    `username` VARCHAR(255) NOT NULL,
    `wins` INT(11) NOT NULL DEFAULT 0,
    `losses` INT(11) NOT NULL DEFAULT 0,
    `kills` INT(11) NOT NULL DEFAULT 0,
    `deaths` INT(11) NOT NULL DEFAULT 0,
    `games_played` INT(11) NOT NULL DEFAULT 0,
    `time_played` BIGINT(20) NOT NULL DEFAULT 0,
    `experience` INT(11) NOT NULL DEFAULT 0,
    `level` INT(11) NOT NULL DEFAULT 1,
    `coins` INT(11) NOT NULL DEFAULT 0,
    `kill_streak_best` INT(11) NOT NULL DEFAULT 0,
    `damage_dealt` BIGINT(20) NOT NULL DEFAULT 0,
    `damage_taken` BIGINT(20) NOT NULL DEFAULT 0,
    `blocks_placed` INT(11) NOT NULL DEFAULT 0,
    `blocks_broken` INT(11) NOT NULL DEFAULT 0,
    `chests_opened` INT(11) NOT NULL DEFAULT 0,
    `arrows_shot` INT(11) NOT NULL DEFAULT 0,
    `arrows_hit` INT(11) NOT NULL DEFAULT 0,
    `first_join` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_game` DATETIME DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uuid` (`uuid`),
    KEY `idx_username` (`username`),
    KEY `idx_wins` (`wins`),
    KEY `idx_kills` (`kills`),
    KEY `idx_level` (`level`),
    KEY `idx_last_game` (`last_game`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create SkyWars match history table
CREATE TABLE IF NOT EXISTS `skywars_matches` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `match_uuid` VARCHAR(36) NOT NULL,
    `arena` VARCHAR(255) NOT NULL,
    `game_mode` VARCHAR(50) NOT NULL,
    `start_time` DATETIME NOT NULL,
    `end_time` DATETIME DEFAULT NULL,
    `duration` INT(11) DEFAULT NULL,
    `winner_uuid` VARCHAR(36) DEFAULT NULL,
    `winner_username` VARCHAR(255) DEFAULT NULL,
    `total_players` INT(11) NOT NULL,
    `total_kills` INT(11) NOT NULL DEFAULT 0,
    `status` ENUM('ACTIVE', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'ACTIVE',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `match_uuid` (`match_uuid`),
    KEY `idx_arena` (`arena`),
    KEY `idx_game_mode` (`game_mode`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_winner` (`winner_uuid`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create SkyWars match participants table
CREATE TABLE IF NOT EXISTS `skywars_match_participants` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `match_id` INT(11) NOT NULL,
    `player_uuid` VARCHAR(36) NOT NULL,
    `player_username` VARCHAR(255) NOT NULL,
    `placement` INT(11) DEFAULT NULL,
    `kills` INT(11) NOT NULL DEFAULT 0,
    `deaths` INT(11) NOT NULL DEFAULT 0,
    `damage_dealt` INT(11) NOT NULL DEFAULT 0,
    `damage_taken` INT(11) NOT NULL DEFAULT 0,
    `survival_time` INT(11) NOT NULL DEFAULT 0,
    `blocks_placed` INT(11) NOT NULL DEFAULT 0,
    `blocks_broken` INT(11) NOT NULL DEFAULT 0,
    `chests_opened` INT(11) NOT NULL DEFAULT 0,
    `arrows_shot` INT(11) NOT NULL DEFAULT 0,
    `arrows_hit` INT(11) NOT NULL DEFAULT 0,
    `experience_gained` INT(11) NOT NULL DEFAULT 0,
    `coins_gained` INT(11) NOT NULL DEFAULT 0,
    `joined_at` DATETIME NOT NULL,
    `left_at` DATETIME DEFAULT NULL,
    `disconnect_reason` VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_match_id` (`match_id`),
    KEY `idx_player_uuid` (`player_uuid`),
    KEY `idx_placement` (`placement`),
    KEY `idx_kills` (`kills`),
    FOREIGN KEY (`match_id`) REFERENCES `skywars_matches` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create SkyWars achievements table
CREATE TABLE IF NOT EXISTS `skywars_achievements` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `achievement_id` VARCHAR(255) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NOT NULL,
    `category` VARCHAR(100) NOT NULL,
    `requirement_type` VARCHAR(50) NOT NULL,
    `requirement_value` INT(11) NOT NULL,
    `reward_xp` INT(11) NOT NULL DEFAULT 0,
    `reward_coins` INT(11) NOT NULL DEFAULT 0,
    `icon` VARCHAR(255) DEFAULT NULL,
    `rarity` ENUM('COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY') NOT NULL DEFAULT 'COMMON',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `achievement_id` (`achievement_id`),
    KEY `idx_category` (`category`),
    KEY `idx_rarity` (`rarity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create SkyWars player achievements table
CREATE TABLE IF NOT EXISTS `skywars_player_achievements` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `player_uuid` VARCHAR(36) NOT NULL,
    `achievement_id` VARCHAR(255) NOT NULL,
    `unlocked_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `progress` INT(11) NOT NULL DEFAULT 0,
    `completed` BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (`id`),
    UNIQUE KEY `player_achievement` (`player_uuid`, `achievement_id`),
    KEY `idx_player_uuid` (`player_uuid`),
    KEY `idx_achievement_id` (`achievement_id`),
    KEY `idx_completed` (`completed`),
    FOREIGN KEY (`achievement_id`) REFERENCES `skywars_achievements` (`achievement_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create SkyWars leaderboards table
CREATE TABLE IF NOT EXISTS `skywars_leaderboards` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `leaderboard_type` VARCHAR(50) NOT NULL,
    `period` ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'ALL_TIME') NOT NULL,
    `player_uuid` VARCHAR(36) NOT NULL,
    `player_username` VARCHAR(255) NOT NULL,
    `value` BIGINT(20) NOT NULL,
    `rank_position` INT(11) NOT NULL,
    `period_start` DATE NOT NULL,
    `period_end` DATE NOT NULL,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `leaderboard_entry` (`leaderboard_type`, `period`, `player_uuid`, `period_start`),
    KEY `idx_leaderboard_type` (`leaderboard_type`),
    KEY `idx_period` (`period`),
    KEY `idx_rank_position` (`rank_position`),
    KEY `idx_period_dates` (`period_start`, `period_end`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Show table structures
DESCRIBE `authme`;
DESCRIBE `users`;
DESCRIBE `player_sessions`;
DESCRIBE `login_attempts`;
DESCRIBE `skywars_players`;
DESCRIBE `skywars_matches`;
DESCRIBE `skywars_match_participants`;
DESCRIBE `skywars_achievements`;
DESCRIBE `skywars_player_achievements`;
DESCRIBE `skywars_leaderboards`;
