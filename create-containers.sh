#!/bin/bash

# 🐳 Simple Container Creation Script
# Creates Minecraft server containers with fresh setup
# Version: 2.1 - Container Creation

echo "🐳 Creating Minecraft Server Containers..."
echo "=========================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Step 1: Check prerequisites
echo "Step 1: Checking prerequisites..."
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed!"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose is not installed!"
    exit 1
fi

if [[ ! -f "docker-compose.yml" ]]; then
    log_error "docker-compose.yml not found!"
    exit 1
fi

log_success "Prerequisites OK"

# Step 2: Create directories
echo ""
echo "Step 2: Creating directories..."
mkdir -p logs backups config data scripts/recovery plugins/SkyWars database
log_success "Directories created"

# Step 3: Pull images
echo ""
echo "Step 3: Pulling Docker images..."
log_info "This may take a few minutes..."
docker-compose pull
log_success "Images pulled"

# Step 4: Start containers
echo ""
echo "Step 4: Starting containers..."
docker-compose up -d
log_success "Containers started"

# Step 5: Wait for startup
echo ""
echo "Step 5: Waiting for services to start..."
log_info "Waiting 30 seconds for initial startup..."
sleep 30

# Check container status
echo ""
echo "Container Status:"
docker-compose ps

# Step 6: Wait for Minecraft server
echo ""
echo "Step 6: Waiting for Minecraft server..."
log_info "Checking server logs for startup completion..."
log_info "This may take 2-5 minutes..."

for i in {1..60}; do
    if docker-compose logs mc | grep -q "Done\|Timings Reset"; then
        log_success "Minecraft server is ready!"
        break
    fi
    echo "   Waiting for server... ($i/60)"
    sleep 5
done

# Step 7: Wait for database
echo ""
echo "Step 7: Waiting for database..."
for i in {1..30}; do
    if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
        log_success "Database is ready!"
        break
    fi
    echo "   Waiting for database... ($i/30)"
    sleep 3
done

# Step 8: Final status
echo ""
echo "🎉 Container Creation Complete!"
echo "==============================="
echo ""
echo "📊 Final Status:"
docker-compose ps
echo ""
echo "🌐 Service Endpoints:"
echo "• Minecraft Server: localhost:25565"
echo "• File Manager: http://localhost:8080"
echo "• Database: localhost:3306"
echo ""
echo "📋 Next Steps:"
echo "1. Run enhancement scripts: ./complete-setup.sh"
echo "2. Or run individual scripts from the scripts/ directory"
echo "3. Connect to server: localhost:25565"
echo ""
log_success "Containers are ready for SkyWars enhancements!"
