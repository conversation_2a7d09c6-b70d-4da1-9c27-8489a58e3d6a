# 🏆 Professional SkyWars Configuration
# Advanced match management system for competitive gameplay
# Version: 2.0 - Professional Edition

# Language and locale settings
locale: en
debug: false
enable-metrics: true
auto-update-check: true

# 🎮 Advanced Game Settings
games:
  # Queue management
  queue:
    # Minimum time to wait for more players (seconds)
    minimum-wait-time: 15
    # Maximum time to wait before force-starting (seconds)
    maximum-wait-time: 120
    # Time before game starts after minimum players reached (seconds)
    countdown-time: 10
    # Enable smart queue balancing
    smart-balancing: true
    # Allow parties to join together
    party-support: true
    # Maximum party size
    max-party-size: 4

  # Match timing
  timing:
    # Grace period after game starts (no PvP, seconds)
    grace-period: 30
    # Maximum game time (seconds) - 0 for no limit
    max-time: 900
    # Time before forced deathmatch (seconds)
    deathmatch-time: 600
    # Sudden death timer (rapid damage, seconds)
    sudden-death-time: 780
    # Border shrink start time (seconds)
    border-shrink-start: 300

  # Player limits per game mode
  player-limits:
    solo:
      min-players: 2
      max-players: 12
      optimal-players: 8
    doubles:
      min-players: 4
      max-players: 24
      optimal-players: 16
    squads:
      min-players: 8
      max-players: 48
      optimal-players: 32

  # Game modes available
  game-modes:
    solo:
      enabled: true
      name: "&6Solo SkyWars"
      description: "&7Fight alone to be the last player standing!"
    doubles:
      enabled: true
      name: "&bDoubles SkyWars"
      description: "&7Team up with a friend and dominate the skies!"
    squads:
      enabled: false
      name: "&cSquads SkyWars"
      description: "&7Form a squad of 4 and conquer together!"
    ranked:
      enabled: false
      name: "&5Ranked SkyWars"
      description: "&7Competitive matches with skill-based matchmaking!"

# 🏟️ Advanced Arena Settings
arenas:
  # Arena restoration
  enable-arena-restoration: true
  restoration-delay: 5
  async-restoration: true

  # Multi-arena support
  max-concurrent-games: 10
  arena-rotation: true
  random-arena-selection: true

  # Arena scaling
  dynamic-scaling: true
  scale-based-on-players: true

  # Border settings
  border:
    enabled: true
    initial-size: 120
    final-size: 20
    shrink-speed: 1
    damage-per-second: 2
    warning-time: 30

# 💰 Enhanced Economy Settings
economy:
  enabled: true
  # Rewards
  rewards:
    win: 150
    kill: 25
    participation: 10
    first-kill: 15
    final-kill: 35
    # Streak bonuses
    kill-streak-2: 10
    kill-streak-3: 20
    kill-streak-5: 50
  # Experience system
  experience:
    enabled: true
    win-xp: 100
    kill-xp: 15
    participation-xp: 25
    level-up-reward: 100

# 📦 Advanced Chest Settings
chests:
  # Chest management
  randomize: true
  refill-chests: true
  refill-time: 180
  max-items-per-chest: 8

  # Chest tiers
  tiers:
    basic:
      probability: 60
      max-items: 4
    premium:
      probability: 30
      max-items: 6
    legendary:
      probability: 10
      max-items: 8

  # Special chests
  center-chests:
    enabled: true
    better-loot: true
    loot-multiplier: 1.5

  # Refill settings
  refill:
    enabled: true
    announce: true
    times: [180, 360, 540]  # 3, 6, 9 minutes
    improve-loot: true

# 👥 Advanced Player Settings
players:
  # Inventory management
  clear-inventory: true
  save-inventory: false
  restore-inventory: true

  # Starting items
  starting-items:
    enabled: true
    items:
      solo:
        - "STONE_SWORD:1"
        - "BREAD:3"
        - "ARROW:8"
        - "OAK_PLANKS:16"
      doubles:
        - "STONE_SWORD:1"
        - "BREAD:4"
        - "ARROW:12"
        - "OAK_PLANKS:20"
        - "GOLDEN_APPLE:1"
      squads:
        - "IRON_SWORD:1"
        - "BREAD:5"
        - "ARROW:16"
        - "OAK_PLANKS:24"
        - "GOLDEN_APPLE:2"

  # Player state management
  state-management:
    handle-disconnects: true
    reconnect-time: 60
    spectate-on-disconnect: true
    save-location: true

  # Combat settings
  combat:
    pvp-delay: 30
    combat-log-protection: true
    kill-rewards: true
    death-messages: true

# 🌍 World Management
worlds:
  lobby-world: lobby
  return-to-lobby: true

  # World protection
  protection:
    prevent-block-break: true
    prevent-block-place: true
    prevent-item-drop: true
    prevent-pickup: false

  # World effects
  effects:
    clear-weather: true
    lock-time: true
    time-value: 6000
    disable-hunger: false

# 👻 Spectator System
spectator:
  enabled: true

  # Spectator features
  features:
    fly-speed: 0.2
    teleport-to-players: true
    player-list: true
    spectator-chat: true
    hide-from-players: true

  # Spectator restrictions
  restrictions:
    prevent-interaction: true
    prevent-damage: true
    prevent-pickup: true

  # Spectator UI
  ui:
    compass-menu: true
    player-tracker: true
    statistics-display: true

# 📊 Statistics System
statistics:
  enabled: true

  # Tracked statistics
  track:
    wins: true
    losses: true
    kills: true
    deaths: true
    games-played: true
    time-played: true
    damage-dealt: true
    damage-taken: true
    blocks-placed: true
    blocks-broken: true
    chests-opened: true

  # Leaderboards
  leaderboards:
    enabled: true
    update-interval: 300  # 5 minutes
    display-top: 10

    # Leaderboard types
    types:
      - wins
      - kills
      - kdr
      - win-rate
      - time-played

# 🎨 User Interface
ui:
  # Lobby integration
  lobby:
    npc-enabled: true
    hologram-enabled: true
    signs-enabled: true

  # In-game UI
  game:
    scoreboard: true
    action-bar: true
    boss-bar: true
    title-messages: true

  # Notifications
  notifications:
    sound-effects: true
    particle-effects: true
    chat-messages: true

# 🔧 Advanced Features
advanced:
  # Anti-cheat integration
  anti-cheat:
    enabled: true
    movement-checks: true
    combat-checks: true

  # Performance optimization
  performance:
    async-operations: true
    chunk-loading: true
    entity-limits: true

  # Database settings
  database:
    enabled: true
    type: "mysql"
    host: "db"
    port: 3306
    database: "minecraft-abusaker"
    username: "hamza"
    password: "Hh@#2021"
    table-prefix: "skywars_"

# 📝 Messages (basic - full messages in messages.yml)
messages:
  prefix: "&8[&b&lSkyWars&8] "
  game-start: "&a&lGame starting! &r&aGood luck!"
  game-end: "&c&lGame ended!"
  player-join: "&e%player% &ajoined the game! &7(&e%current%&7/&e%max%&7)"
  player-leave: "&e%player% &cleft the game!"
  queue-join: "&aYou joined the SkyWars queue! &7Position: &e#%position%"
  queue-leave: "&cYou left the SkyWars queue!"
