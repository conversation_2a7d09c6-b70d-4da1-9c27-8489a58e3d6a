#!/bin/bash

# ✅ SkyWars Validation and Testing System
# Comprehensive validation and data integrity checks
# Version: 2.1 - Enhanced Validation

set -e

echo "✅ Setting up SkyWars Validation System..."
echo "=========================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_validate() { echo -e "${PURPLE}✅ $1${NC}"; }

# Global validation counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a validation test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="${3:-0}"
    
    ((TOTAL_TESTS++))
    
    echo -n "Testing: $test_name... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        local result=$?
        if [[ $result -eq $expected_result ]]; then
            echo -e "${GREEN}PASS${NC}"
            ((PASSED_TESTS++))
            return 0
        else
            echo -e "${RED}FAIL${NC} (Expected: $expected_result, Got: $result)"
            ((FAILED_TESTS++))
            return 1
        fi
    else
        echo -e "${RED}FAIL${NC} (Command failed)"
        ((FAILED_TESTS++))
        return 1
    fi
}

# Function to validate YAML configuration files
validate_yaml_configs() {
    log_validate "Validating YAML configuration files..."
    
    local config_files=(
        "plugins/SkyWars/config.yml"
        "plugins/SkyWars/arenas.yml"
        "plugins/SkyWars/main-config.yml"
        "plugins/AuthMe/config.yml"
        "plugins/Essentials/config.yml"
        "plugins/Multiverse-Core/config.yml"
    )
    
    for config in "${config_files[@]}"; do
        if [[ -f "$config" ]]; then
            run_test "YAML syntax: $(basename $config)" "python3 -c \"import yaml; yaml.safe_load(open('$config'))\""
        else
            log_warning "Configuration file not found: $config"
        fi
    done
}

# Function to validate Docker environment
validate_docker_environment() {
    log_validate "Validating Docker environment..."
    
    run_test "Docker daemon running" "docker info"
    run_test "Docker Compose available" "docker-compose --version"
    run_test "Minecraft server container" "docker ps | grep minecraft-server-docker-mc-1"
    run_test "Database container" "docker ps | grep minecraft-server-docker-db-1"
    
    # Check container health
    run_test "Server container health" "docker inspect minecraft-server-docker-mc-1 | grep '\"Status\": \"running\"'"
    run_test "Database container health" "docker inspect minecraft-server-docker-db-1 | grep '\"Status\": \"running\"'"
}

# Function to validate database connectivity
validate_database() {
    log_validate "Validating database connectivity..."
    
    run_test "Database ping" "docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p'Hh@#2021' --silent"
    run_test "Database connection" "docker exec minecraft-server-docker-db-1 mysql -u hamza -p'Hh@#2021' minecraft-abusaker -e 'SELECT 1;'"
    
    # Check for required tables
    local tables=("authme" "users" "skywars_stats")
    for table in "${tables[@]}"; do
        run_test "Table exists: $table" "docker exec minecraft-server-docker-db-1 mysql -u hamza -p'Hh@#2021' minecraft-abusaker -e 'DESCRIBE $table;'"
    done
}

# Function to validate server performance
validate_server_performance() {
    log_validate "Validating server performance..."
    
    # Check TPS
    local tps=$(docker exec minecraft-server-docker-mc-1 rcon-cli "tps" 2>/dev/null | grep -o '[0-9]*\.[0-9]*' | head -1 || echo "0")
    if (( $(echo "$tps >= 18.0" | bc -l) )); then
        run_test "Server TPS ($tps)" "true"
    else
        run_test "Server TPS ($tps)" "false"
    fi
    
    # Check memory usage
    local memory_percent=$(docker stats minecraft-server-docker-mc-1 --no-stream --format "{{.MemPerc}}" 2>/dev/null | sed 's/%//' || echo "100")
    if (( $(echo "$memory_percent <= 80" | bc -l) )); then
        run_test "Memory usage (${memory_percent}%)" "true"
    else
        run_test "Memory usage (${memory_percent}%)" "false"
    fi
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -lt 90 ]]; then
        run_test "Disk usage (${disk_usage}%)" "true"
    else
        run_test "Disk usage (${disk_usage}%)" "false"
    fi
}

# Function to validate plugin functionality
validate_plugins() {
    log_validate "Validating plugin functionality..."
    
    # Get plugin list
    local plugins=$(docker exec minecraft-server-docker-mc-1 rcon-cli "plugins" 2>/dev/null || echo "")
    
    # Check required plugins
    local required_plugins=("AuthMe" "Essentials" "Multiverse-Core")
    for plugin in "${required_plugins[@]}"; do
        if echo "$plugins" | grep -q "$plugin"; then
            run_test "Plugin loaded: $plugin" "true"
        else
            run_test "Plugin loaded: $plugin" "false"
        fi
    done
    
    # Test plugin commands
    run_test "AuthMe command" "docker exec minecraft-server-docker-mc-1 rcon-cli 'authme version'"
    run_test "Essentials command" "docker exec minecraft-server-docker-mc-1 rcon-cli 'essentials version'"
    run_test "Multiverse command" "docker exec minecraft-server-docker-mc-1 rcon-cli 'mv version'"
}

# Function to validate world setup
validate_worlds() {
    log_validate "Validating world setup..."
    
    # Check world directories
    run_test "Lobby world exists" "[[ -d 'lobby' ]]"
    run_test "SkyWars world exists" "[[ -d 'modern_skywar' ]]"
    
    # Check world in Multiverse
    local worlds=$(docker exec minecraft-server-docker-mc-1 rcon-cli "mv list" 2>/dev/null || echo "")
    if echo "$worlds" | grep -q "lobby"; then
        run_test "Lobby world in Multiverse" "true"
    else
        run_test "Lobby world in Multiverse" "false"
    fi
    
    if echo "$worlds" | grep -q "modern_skywar"; then
        run_test "SkyWars world in Multiverse" "true"
    else
        run_test "SkyWars world in Multiverse" "false"
    fi
}

# Function to validate file permissions
validate_permissions() {
    log_validate "Validating file permissions..."
    
    # Check script permissions
    local scripts=(
        "scripts/fix-skywars-system.sh"
        "scripts/error-handling-system.sh"
        "scripts/plugin-integration.sh"
        "scripts/validation-system.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]]; then
            run_test "Script executable: $(basename $script)" "[[ -x '$script' ]]"
        fi
    done
    
    # Check directory permissions
    run_test "Plugins directory writable" "[[ -w 'plugins' ]]"
    run_test "Scripts directory writable" "[[ -w 'scripts' ]]"
}

# Function to validate network connectivity
validate_network() {
    log_validate "Validating network connectivity..."
    
    # Check container network
    run_test "Container network exists" "docker network ls | grep minecraft_net"
    
    # Check port accessibility
    run_test "Minecraft port accessible" "nc -z localhost 25565"
    run_test "RCON port accessible" "nc -z localhost 25575"
    run_test "Database port accessible" "nc -z localhost 3306"
    run_test "File manager port accessible" "nc -z localhost 8080"
}

# Function to validate security settings
validate_security() {
    log_validate "Validating security settings..."
    
    # Check RCON password
    if grep -q "RCON_PASSWORD" docker-compose.yml; then
        run_test "RCON password set" "true"
    else
        run_test "RCON password set" "false"
    fi
    
    # Check database password
    if grep -q "MYSQL_ROOT_PASSWORD" docker-compose.yml; then
        run_test "Database password set" "true"
    else
        run_test "Database password set" "false"
    fi
    
    # Check file permissions on sensitive files
    run_test "Docker compose permissions" "[[ $(stat -c '%a' docker-compose.yml) -le 644 ]]"
}

# Function to create comprehensive test report
create_test_report() {
    log_validate "Creating test report..."
    
    local report_file="validation-report-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
SkyWars System Validation Report
================================
Date: $(date)
Total Tests: $TOTAL_TESTS
Passed: $PASSED_TESTS
Failed: $FAILED_TESTS
Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%

System Status: $(if [[ $FAILED_TESTS -eq 0 ]]; then echo "HEALTHY"; else echo "NEEDS ATTENTION"; fi)

Test Categories:
- YAML Configuration Validation
- Docker Environment Validation
- Database Connectivity Validation
- Server Performance Validation
- Plugin Functionality Validation
- World Setup Validation
- File Permissions Validation
- Network Connectivity Validation
- Security Settings Validation

Recommendations:
$(if [[ $FAILED_TESTS -eq 0 ]]; then
    echo "✅ All tests passed! System is ready for production."
else
    echo "❌ $FAILED_TESTS test(s) failed. Please review and fix issues before deployment."
fi)

For detailed logs, check the console output above.
EOF
    
    log_success "Test report saved to: $report_file"
}

# Function to run integration tests
run_integration_tests() {
    log_validate "Running integration tests..."
    
    # Test SkyWars command integration
    if [[ -f "scripts/skywars-unified-commands.sh" ]]; then
        run_test "SkyWars commands script exists" "true"
        run_test "SkyWars commands executable" "[[ -x 'scripts/skywars-unified-commands.sh' ]]"
    fi
    
    # Test error handling system
    if [[ -f "scripts/error-handling-system.sh" ]]; then
        run_test "Error handling system exists" "true"
        run_test "Error handling executable" "[[ -x 'scripts/error-handling-system.sh' ]]"
    fi
    
    # Test monitoring system
    if [[ -f "scripts/monitor-skywars.sh" ]]; then
        run_test "Monitoring system exists" "true"
        run_test "Monitoring executable" "[[ -x 'scripts/monitor-skywars.sh' ]]"
    fi
}

# Function to validate data integrity
validate_data_integrity() {
    log_validate "Validating data integrity..."
    
    # Check configuration file integrity
    local critical_configs=(
        "docker-compose.yml"
        "plugins/AuthMe/config.yml"
        "plugins/Essentials/config.yml"
    )
    
    for config in "${critical_configs[@]}"; do
        if [[ -f "$config" ]]; then
            run_test "Config file not empty: $(basename $config)" "[[ -s '$config' ]]"
        fi
    done
    
    # Check database data integrity
    if docker exec minecraft-server-docker-db-1 mysql -u hamza -p"Hh@#2021" minecraft-abusaker -e "SELECT COUNT(*) FROM authme;" >/dev/null 2>&1; then
        run_test "AuthMe table accessible" "true"
    else
        run_test "AuthMe table accessible" "false"
    fi
}

# Main execution function
main() {
    log_info "Starting comprehensive SkyWars system validation..."
    echo ""
    
    # Run all validation categories
    validate_yaml_configs
    echo ""
    
    validate_docker_environment
    echo ""
    
    validate_database
    echo ""
    
    validate_server_performance
    echo ""
    
    validate_plugins
    echo ""
    
    validate_worlds
    echo ""
    
    validate_permissions
    echo ""
    
    validate_network
    echo ""
    
    validate_security
    echo ""
    
    run_integration_tests
    echo ""
    
    validate_data_integrity
    echo ""
    
    # Generate summary
    echo "=========================================="
    echo "VALIDATION SUMMARY"
    echo "=========================================="
    echo "Total Tests: $TOTAL_TESTS"
    echo "Passed: $PASSED_TESTS"
    echo "Failed: $FAILED_TESTS"
    echo "Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    echo ""
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "🎉 All validation tests passed! System is ready!"
        echo ""
        echo "✅ System Status: HEALTHY"
        echo "✅ Ready for production use"
        echo "✅ All components functioning correctly"
    else
        log_warning "⚠️  Some validation tests failed"
        echo ""
        echo "❌ System Status: NEEDS ATTENTION"
        echo "❌ $FAILED_TESTS issue(s) need to be resolved"
        echo "❌ Please fix issues before production use"
    fi
    
    # Create detailed report
    create_test_report
    
    echo ""
    log_success "Validation system setup complete! ✅"
    
    # Return appropriate exit code
    if [[ $FAILED_TESTS -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# Run main function
main "$@"
