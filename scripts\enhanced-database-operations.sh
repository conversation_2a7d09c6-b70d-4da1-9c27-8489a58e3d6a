#!/bin/bash

# 🗄️ Enhanced SkyWars Database Operations
# Advanced transaction handling and connection management
# Version: 2.1 - Enhanced Database Operations

set -e

echo "🗄️ Setting up Enhanced Database Operations..."
echo "============================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_db() { echo -e "${PURPLE}🗄️ $1${NC}"; }

# Database connection configuration
DB_HOST="db"
DB_PORT="3306"
DB_NAME="minecraft-abusaker"
DB_USER="hamza"
DB_PASS="Hh@#2021"
DB_CONTAINER="minecraft-server-docker-db-1"

# Create enhanced database connection manager
create_connection_manager() {
    log_db "Creating database connection manager..."
    
    cat > scripts/db-connection-manager.sh << 'EOF'
#!/bin/bash
# Database Connection Manager
# Handles connection pooling and management

# Database configuration
DB_HOST="db"
DB_PORT="3306"
DB_NAME="minecraft-abusaker"
DB_USER="hamza"
DB_PASS="Hh@#2021"
DB_CONTAINER="minecraft-server-docker-db-1"

# Connection pool settings
MAX_CONNECTIONS=10
CONNECTION_TIMEOUT=30
RETRY_ATTEMPTS=3
RETRY_DELAY=2

# Function to test database connection
test_db_connection() {
    local attempt=1
    
    while [[ $attempt -le $RETRY_ATTEMPTS ]]; do
        if docker exec "$DB_CONTAINER" mysqladmin ping -h localhost -u "$DB_USER" -p"$DB_PASS" --silent 2>/dev/null; then
            return 0
        fi
        
        echo "Connection attempt $attempt failed, retrying in ${RETRY_DELAY}s..."
        sleep $RETRY_DELAY
        ((attempt++))
    done
    
    return 1
}

# Function to execute SQL with connection management
execute_sql() {
    local sql="$1"
    local database="${2:-$DB_NAME}"
    
    if ! test_db_connection; then
        echo "Error: Cannot establish database connection"
        return 1
    fi
    
    # Execute SQL with timeout
    timeout $CONNECTION_TIMEOUT docker exec -i "$DB_CONTAINER" mysql -u "$DB_USER" -p"$DB_PASS" "$database" << EOF
$sql
EOF
}

# Function to execute SQL file
execute_sql_file() {
    local sql_file="$1"
    local database="${2:-$DB_NAME}"
    
    if [[ ! -f "$sql_file" ]]; then
        echo "Error: SQL file not found: $sql_file"
        return 1
    fi
    
    if ! test_db_connection; then
        echo "Error: Cannot establish database connection"
        return 1
    fi
    
    timeout $CONNECTION_TIMEOUT docker exec -i "$DB_CONTAINER" mysql -u "$DB_USER" -p"$DB_PASS" "$database" < "$sql_file"
}

# Function to get connection status
get_connection_status() {
    local connections=$(execute_sql "SHOW STATUS LIKE 'Threads_connected';" | grep -o '[0-9]*' || echo "0")
    local max_connections=$(execute_sql "SHOW VARIABLES LIKE 'max_connections';" | grep -o '[0-9]*' || echo "0")
    
    echo "Active connections: $connections/$max_connections"
    
    if [[ $connections -gt $((max_connections * 80 / 100)) ]]; then
        echo "Warning: High connection usage"
        return 1
    fi
    
    return 0
}

# Export functions
export -f test_db_connection
export -f execute_sql
export -f execute_sql_file
export -f get_connection_status
EOF

    chmod +x scripts/db-connection-manager.sh
    log_success "Database connection manager created"
}

# Create transaction management system
create_transaction_manager() {
    log_db "Creating transaction management system..."
    
    cat > scripts/db-transaction-manager.sh << 'EOF'
#!/bin/bash
# Database Transaction Manager
# Handles ACID transactions for SkyWars operations

# Source connection manager
source scripts/db-connection-manager.sh

# Transaction state tracking
TRANSACTION_ACTIVE=false
TRANSACTION_ID=""

# Function to start transaction
start_transaction() {
    local transaction_name="${1:-skywars_tx_$(date +%s)}"
    
    if [[ "$TRANSACTION_ACTIVE" == "true" ]]; then
        echo "Warning: Transaction already active: $TRANSACTION_ID"
        return 1
    fi
    
    if execute_sql "START TRANSACTION;"; then
        TRANSACTION_ACTIVE=true
        TRANSACTION_ID="$transaction_name"
        echo "Transaction started: $TRANSACTION_ID"
        return 0
    else
        echo "Error: Failed to start transaction"
        return 1
    fi
}

# Function to commit transaction
commit_transaction() {
    if [[ "$TRANSACTION_ACTIVE" != "true" ]]; then
        echo "Warning: No active transaction to commit"
        return 1
    fi
    
    if execute_sql "COMMIT;"; then
        TRANSACTION_ACTIVE=false
        echo "Transaction committed: $TRANSACTION_ID"
        TRANSACTION_ID=""
        return 0
    else
        echo "Error: Failed to commit transaction"
        return 1
    fi
}

# Function to rollback transaction
rollback_transaction() {
    if [[ "$TRANSACTION_ACTIVE" != "true" ]]; then
        echo "Warning: No active transaction to rollback"
        return 1
    fi
    
    if execute_sql "ROLLBACK;"; then
        TRANSACTION_ACTIVE=false
        echo "Transaction rolled back: $TRANSACTION_ID"
        TRANSACTION_ID=""
        return 0
    else
        echo "Error: Failed to rollback transaction"
        return 1
    fi
}

# Function to execute SQL within transaction
execute_in_transaction() {
    local sql="$1"
    local auto_commit="${2:-true}"
    
    if [[ "$TRANSACTION_ACTIVE" != "true" ]]; then
        if ! start_transaction; then
            return 1
        fi
        local started_here=true
    fi
    
    if execute_sql "$sql"; then
        if [[ "$auto_commit" == "true" && "$started_here" == "true" ]]; then
            commit_transaction
        fi
        return 0
    else
        echo "Error: SQL execution failed in transaction"
        if [[ "$started_here" == "true" ]]; then
            rollback_transaction
        fi
        return 1
    fi
}

# Function for safe player stats update
update_player_stats_safe() {
    local player_uuid="$1"
    local wins="$2"
    local losses="$3"
    local kills="$4"
    local deaths="$5"
    
    local sql="
    INSERT INTO skywars_players (uuid, wins, losses, kills, deaths, last_game)
    VALUES ('$player_uuid', $wins, $losses, $kills, $deaths, NOW())
    ON DUPLICATE KEY UPDATE
        wins = wins + $wins,
        losses = losses + $losses,
        kills = kills + $kills,
        deaths = deaths + $deaths,
        last_game = NOW();
    "
    
    execute_in_transaction "$sql"
}

# Function for safe match recording
record_match_safe() {
    local match_uuid="$1"
    local arena="$2"
    local winner_uuid="$3"
    local players="$4"
    local duration="$5"
    
    start_transaction "match_record_$match_uuid"
    
    # Insert match record
    local match_sql="
    INSERT INTO skywars_matches (match_uuid, arena, winner_uuid, total_players, duration, start_time, end_time, status)
    VALUES ('$match_uuid', '$arena', '$winner_uuid', $players, $duration, 
            DATE_SUB(NOW(), INTERVAL $duration SECOND), NOW(), 'COMPLETED');
    "
    
    if execute_sql "$match_sql"; then
        commit_transaction
        echo "Match recorded successfully: $match_uuid"
        return 0
    else
        rollback_transaction
        echo "Error: Failed to record match: $match_uuid"
        return 1
    fi
}

# Export functions
export -f start_transaction
export -f commit_transaction
export -f rollback_transaction
export -f execute_in_transaction
export -f update_player_stats_safe
export -f record_match_safe
EOF

    chmod +x scripts/db-transaction-manager.sh
    log_success "Transaction management system created"
}

# Create database optimization system
create_db_optimization() {
    log_db "Creating database optimization system..."
    
    cat > scripts/db-optimization.sh << 'EOF'
#!/bin/bash
# Database Optimization System
# Performance tuning and maintenance

# Source connection manager
source scripts/db-connection-manager.sh

# Function to optimize database tables
optimize_tables() {
    echo "Optimizing SkyWars database tables..."
    
    local tables=(
        "skywars_players"
        "skywars_matches"
        "skywars_match_participants"
        "skywars_achievements"
        "skywars_player_achievements"
        "skywars_leaderboards"
    )
    
    for table in "${tables[@]}"; do
        echo "Optimizing table: $table"
        if execute_sql "OPTIMIZE TABLE $table;"; then
            echo "✅ $table optimized"
        else
            echo "⚠️  Failed to optimize $table"
        fi
    done
}

# Function to analyze table performance
analyze_tables() {
    echo "Analyzing table performance..."
    
    local analysis_sql="
    SELECT 
        table_name,
        table_rows,
        data_length,
        index_length,
        (data_length + index_length) as total_size
    FROM information_schema.tables 
    WHERE table_schema = '$DB_NAME' 
    AND table_name LIKE 'skywars_%'
    ORDER BY total_size DESC;
    "
    
    execute_sql "$analysis_sql"
}

# Function to create performance indexes
create_performance_indexes() {
    echo "Creating performance indexes..."
    
    local index_sql="
    -- Indexes for skywars_players
    CREATE INDEX IF NOT EXISTS idx_player_wins ON skywars_players(wins DESC);
    CREATE INDEX IF NOT EXISTS idx_player_kills ON skywars_players(kills DESC);
    CREATE INDEX IF NOT EXISTS idx_player_last_game ON skywars_players(last_game);
    
    -- Indexes for skywars_matches
    CREATE INDEX IF NOT EXISTS idx_match_arena ON skywars_matches(arena);
    CREATE INDEX IF NOT EXISTS idx_match_start_time ON skywars_matches(start_time);
    CREATE INDEX IF NOT EXISTS idx_match_winner ON skywars_matches(winner_uuid);
    
    -- Indexes for skywars_match_participants
    CREATE INDEX IF NOT EXISTS idx_participant_match ON skywars_match_participants(match_id);
    CREATE INDEX IF NOT EXISTS idx_participant_player ON skywars_match_participants(player_uuid);
    CREATE INDEX IF NOT EXISTS idx_participant_placement ON skywars_match_participants(placement);
    
    -- Composite indexes for common queries
    CREATE INDEX IF NOT EXISTS idx_player_stats ON skywars_players(wins, kills, deaths);
    CREATE INDEX IF NOT EXISTS idx_match_summary ON skywars_matches(arena, start_time, status);
    "
    
    execute_sql "$index_sql"
}

# Function to clean old data
cleanup_old_data() {
    local days_to_keep="${1:-90}"
    
    echo "Cleaning data older than $days_to_keep days..."
    
    local cleanup_sql="
    -- Clean old match data
    DELETE FROM skywars_matches 
    WHERE start_time < DATE_SUB(NOW(), INTERVAL $days_to_keep DAY);
    
    -- Clean old participant data (orphaned records)
    DELETE p FROM skywars_match_participants p
    LEFT JOIN skywars_matches m ON p.match_id = m.id
    WHERE m.id IS NULL;
    
    -- Clean old leaderboard data
    DELETE FROM skywars_leaderboards 
    WHERE period_end < DATE_SUB(NOW(), INTERVAL $days_to_keep DAY);
    "
    
    execute_in_transaction "$cleanup_sql"
}

# Function to backup database
backup_database() {
    local backup_dir="backups/database"
    local backup_file="$backup_dir/skywars_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    mkdir -p "$backup_dir"
    
    echo "Creating database backup: $backup_file"
    
    if docker exec "$DB_CONTAINER" mysqldump -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$backup_file"; then
        echo "✅ Database backup created: $backup_file"
        
        # Compress backup
        gzip "$backup_file"
        echo "✅ Backup compressed: ${backup_file}.gz"
        
        # Clean old backups (keep last 7 days)
        find "$backup_dir" -name "*.gz" -mtime +7 -delete
        
        return 0
    else
        echo "❌ Database backup failed"
        return 1
    fi
}

# Function to monitor database performance
monitor_performance() {
    echo "Database Performance Monitor"
    echo "==========================="
    
    # Get connection status
    get_connection_status
    
    # Get query performance
    local slow_queries=$(execute_sql "SHOW STATUS LIKE 'Slow_queries';" | grep -o '[0-9]*' || echo "0")
    echo "Slow queries: $slow_queries"
    
    # Get table sizes
    echo ""
    echo "Table Sizes:"
    analyze_tables
    
    # Get recent activity
    echo ""
    echo "Recent Activity:"
    local recent_matches=$(execute_sql "SELECT COUNT(*) FROM skywars_matches WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);" | grep -o '[0-9]*' || echo "0")
    echo "Matches in last hour: $recent_matches"
    
    local active_players=$(execute_sql "SELECT COUNT(*) FROM skywars_players WHERE last_game > DATE_SUB(NOW(), INTERVAL 1 DAY);" | grep -o '[0-9]*' || echo "0")
    echo "Active players (24h): $active_players"
}

# Function to run maintenance
run_maintenance() {
    echo "Running database maintenance..."
    
    # Backup first
    backup_database
    
    # Optimize tables
    optimize_tables
    
    # Create/update indexes
    create_performance_indexes
    
    # Clean old data
    cleanup_old_data 90
    
    # Monitor performance
    monitor_performance
    
    echo "✅ Database maintenance completed"
}

# Export functions
export -f optimize_tables
export -f analyze_tables
export -f create_performance_indexes
export -f cleanup_old_data
export -f backup_database
export -f monitor_performance
export -f run_maintenance
EOF

    chmod +x scripts/db-optimization.sh
    log_success "Database optimization system created"
}

# Create database health monitoring
create_db_monitoring() {
    log_db "Creating database health monitoring..."
    
    cat > scripts/db-health-monitor.sh << 'EOF'
#!/bin/bash
# Database Health Monitor
# Continuous monitoring of database health

# Source connection manager
source scripts/db-connection-manager.sh

# Health check thresholds
MAX_CONNECTION_PERCENT=80
MAX_SLOW_QUERIES=10
MAX_RESPONSE_TIME=1000  # milliseconds

# Function to check database health
check_db_health() {
    local health_score=100
    local issues=()
    
    echo "Database Health Check"
    echo "===================="
    
    # Check connection
    if ! test_db_connection; then
        health_score=$((health_score - 50))
        issues+=("Database connection failed")
    else
        echo "✅ Database connection: OK"
    fi
    
    # Check connection usage
    local connections=$(execute_sql "SHOW STATUS LIKE 'Threads_connected';" | grep -o '[0-9]*' || echo "0")
    local max_connections=$(execute_sql "SHOW VARIABLES LIKE 'max_connections';" | grep -o '[0-9]*' || echo "100")
    local connection_percent=$((connections * 100 / max_connections))
    
    if [[ $connection_percent -gt $MAX_CONNECTION_PERCENT ]]; then
        health_score=$((health_score - 20))
        issues+=("High connection usage: ${connection_percent}%")
    else
        echo "✅ Connection usage: ${connection_percent}% (${connections}/${max_connections})"
    fi
    
    # Check slow queries
    local slow_queries=$(execute_sql "SHOW STATUS LIKE 'Slow_queries';" | grep -o '[0-9]*' || echo "0")
    if [[ $slow_queries -gt $MAX_SLOW_QUERIES ]]; then
        health_score=$((health_score - 15))
        issues+=("High slow query count: $slow_queries")
    else
        echo "✅ Slow queries: $slow_queries"
    fi
    
    # Check response time
    local start_time=$(date +%s%3N)
    execute_sql "SELECT 1;" >/dev/null
    local end_time=$(date +%s%3N)
    local response_time=$((end_time - start_time))
    
    if [[ $response_time -gt $MAX_RESPONSE_TIME ]]; then
        health_score=$((health_score - 10))
        issues+=("High response time: ${response_time}ms")
    else
        echo "✅ Response time: ${response_time}ms"
    fi
    
    # Check disk space
    local disk_usage=$(docker exec "$DB_CONTAINER" df /var/lib/mysql | awk 'NR==2 {print $5}' | sed 's/%//' || echo "0")
    if [[ $disk_usage -gt 90 ]]; then
        health_score=$((health_score - 15))
        issues+=("High disk usage: ${disk_usage}%")
    else
        echo "✅ Disk usage: ${disk_usage}%"
    fi
    
    # Overall health assessment
    echo ""
    echo "Health Score: $health_score/100"
    
    if [[ ${#issues[@]} -gt 0 ]]; then
        echo "Issues found:"
        for issue in "${issues[@]}"; do
            echo "  ❌ $issue"
        done
    fi
    
    # Return health status
    if [[ $health_score -ge 80 ]]; then
        echo "Status: HEALTHY"
        return 0
    elif [[ $health_score -ge 60 ]]; then
        echo "Status: WARNING"
        return 1
    else
        echo "Status: CRITICAL"
        return 2
    fi
}

# Function to start continuous monitoring
start_continuous_monitoring() {
    echo "Starting continuous database monitoring..."
    
    while true; do
        sleep 300  # Check every 5 minutes
        
        echo "$(date): Running database health check..."
        if ! check_db_health > "logs/db-health-$(date +%Y%m%d).log" 2>&1; then
            echo "$(date): Database health issues detected" >> "logs/db-alerts.log"
        fi
    done
}

# Export functions
export -f check_db_health
export -f start_continuous_monitoring
EOF

    chmod +x scripts/db-health-monitor.sh
    log_success "Database health monitoring created"
}

# Create enhanced database setup
create_enhanced_db_setup() {
    log_db "Creating enhanced database setup..."
    
    cat > database/enhanced_skywars_schema.sql << 'EOF'
-- Enhanced SkyWars Database Schema
-- Optimized for performance and reliability

-- Enhanced players table with better indexing
CREATE TABLE IF NOT EXISTS `skywars_players_enhanced` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `uuid` VARCHAR(36) NOT NULL,
    `username` VARCHAR(255) NOT NULL,
    `wins` INT(11) NOT NULL DEFAULT 0,
    `losses` INT(11) NOT NULL DEFAULT 0,
    `kills` INT(11) NOT NULL DEFAULT 0,
    `deaths` INT(11) NOT NULL DEFAULT 0,
    `games_played` INT(11) NOT NULL DEFAULT 0,
    `time_played` BIGINT(20) NOT NULL DEFAULT 0,
    `experience` INT(11) NOT NULL DEFAULT 0,
    `level` INT(11) NOT NULL DEFAULT 1,
    `coins` INT(11) NOT NULL DEFAULT 0,
    `kill_streak_best` INT(11) NOT NULL DEFAULT 0,
    `damage_dealt` BIGINT(20) NOT NULL DEFAULT 0,
    `damage_taken` BIGINT(20) NOT NULL DEFAULT 0,
    `first_join` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_game` DATETIME DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uuid` (`uuid`),
    KEY `idx_username` (`username`),
    KEY `idx_wins` (`wins` DESC),
    KEY `idx_kills` (`kills` DESC),
    KEY `idx_level` (`level` DESC),
    KEY `idx_last_game` (`last_game`),
    KEY `idx_stats_composite` (`wins`, `kills`, `deaths`),
    KEY `idx_activity` (`last_game`, `games_played`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced matches table with better performance
CREATE TABLE IF NOT EXISTS `skywars_matches_enhanced` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `match_uuid` VARCHAR(36) NOT NULL,
    `arena` VARCHAR(255) NOT NULL,
    `game_mode` VARCHAR(50) NOT NULL,
    `start_time` DATETIME NOT NULL,
    `end_time` DATETIME DEFAULT NULL,
    `duration` INT(11) DEFAULT NULL,
    `winner_uuid` VARCHAR(36) DEFAULT NULL,
    `winner_username` VARCHAR(255) DEFAULT NULL,
    `total_players` INT(11) NOT NULL,
    `total_kills` INT(11) NOT NULL DEFAULT 0,
    `status` ENUM('WAITING', 'STARTING', 'ACTIVE', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'WAITING',
    `server_id` VARCHAR(50) DEFAULT 'default',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `match_uuid` (`match_uuid`),
    KEY `idx_arena` (`arena`),
    KEY `idx_game_mode` (`game_mode`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_winner` (`winner_uuid`),
    KEY `idx_status` (`status`),
    KEY `idx_server` (`server_id`),
    KEY `idx_match_summary` (`arena`, `start_time`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Performance monitoring table
CREATE TABLE IF NOT EXISTS `skywars_performance_log` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `metric_name` VARCHAR(100) NOT NULL,
    `metric_value` DECIMAL(10,2) NOT NULL,
    `server_id` VARCHAR(50) DEFAULT 'default',
    PRIMARY KEY (`id`),
    KEY `idx_timestamp` (`timestamp`),
    KEY `idx_metric` (`metric_name`),
    KEY `idx_server` (`server_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Database health log
CREATE TABLE IF NOT EXISTS `skywars_db_health` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `check_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `health_score` INT(3) NOT NULL,
    `connection_count` INT(11) DEFAULT NULL,
    `slow_queries` INT(11) DEFAULT NULL,
    `response_time_ms` INT(11) DEFAULT NULL,
    `disk_usage_percent` INT(3) DEFAULT NULL,
    `issues` TEXT DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_check_time` (`check_time`),
    KEY `idx_health_score` (`health_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create views for common queries
CREATE OR REPLACE VIEW `skywars_player_rankings` AS
SELECT 
    uuid,
    username,
    wins,
    losses,
    kills,
    deaths,
    CASE WHEN deaths > 0 THEN ROUND(kills / deaths, 2) ELSE kills END as kdr,
    CASE WHEN games_played > 0 THEN ROUND((wins / games_played) * 100, 1) ELSE 0 END as win_rate,
    level,
    experience,
    RANK() OVER (ORDER BY wins DESC) as wins_rank,
    RANK() OVER (ORDER BY kills DESC) as kills_rank,
    RANK() OVER (ORDER BY CASE WHEN deaths > 0 THEN kills / deaths ELSE kills END DESC) as kdr_rank
FROM skywars_players_enhanced
WHERE games_played > 0;

-- Create stored procedures for common operations
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS UpdatePlayerStats(
    IN p_uuid VARCHAR(36),
    IN p_username VARCHAR(255),
    IN p_wins INT,
    IN p_losses INT,
    IN p_kills INT,
    IN p_deaths INT,
    IN p_experience INT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    INSERT INTO skywars_players_enhanced (
        uuid, username, wins, losses, kills, deaths, experience, games_played, last_game
    ) VALUES (
        p_uuid, p_username, p_wins, p_losses, p_kills, p_deaths, p_experience, 1, NOW()
    ) ON DUPLICATE KEY UPDATE
        username = p_username,
        wins = wins + p_wins,
        losses = losses + p_losses,
        kills = kills + p_kills,
        deaths = deaths + p_deaths,
        experience = experience + p_experience,
        games_played = games_played + 1,
        last_game = NOW(),
        updated_at = CURRENT_TIMESTAMP;
    
    COMMIT;
END //

DELIMITER ;
EOF

    log_success "Enhanced database setup created"
}

# Main execution
main() {
    log_info "Setting up enhanced database operations..."
    
    # Create all database enhancement components
    create_connection_manager
    create_transaction_manager
    create_db_optimization
    create_db_monitoring
    create_enhanced_db_setup
    
    # Test database connection
    log_info "Testing database connection..."
    if ./scripts/db-connection-manager.sh && source scripts/db-connection-manager.sh && test_db_connection; then
        log_success "Database connection test passed"
    else
        log_warning "Database connection test failed - check database status"
    fi
    
    # Run initial optimization
    log_info "Running initial database optimization..."
    if source scripts/db-optimization.sh && create_performance_indexes; then
        log_success "Database optimization completed"
    else
        log_warning "Database optimization had issues"
    fi
    
    log_success "🗄️ Enhanced database operations setup complete!"
    echo ""
    echo "✅ Database Features:"
    echo "   • Advanced connection management"
    echo "   • ACID transaction support"
    echo "   • Performance optimization"
    echo "   • Health monitoring"
    echo "   • Automatic backups"
    echo "   • Query optimization"
    echo ""
    echo "🔧 Available Tools:"
    echo "   • ./scripts/db-connection-manager.sh - Connection management"
    echo "   • ./scripts/db-transaction-manager.sh - Transaction handling"
    echo "   • ./scripts/db-optimization.sh - Performance optimization"
    echo "   • ./scripts/db-health-monitor.sh - Health monitoring"
    echo ""
    echo "📊 Database Operations:"
    echo "   • Connection pooling and timeout handling"
    echo "   • Transaction rollback on errors"
    echo "   • Automatic table optimization"
    echo "   • Performance index creation"
    echo "   • Health score monitoring"
    echo "   • Automated backup system"
    echo ""
    log_success "Enhanced database operations are ready! 🗄️"
}

# Run main function
main "$@"
