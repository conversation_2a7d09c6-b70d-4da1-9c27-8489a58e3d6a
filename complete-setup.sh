#!/bin/bash

# 🚀 Complete SkyWars Setup Script
# Creates containers and runs all enhancement scripts
# Version: 2.1 - Complete Setup

set -e

echo "🚀 Starting Complete SkyWars Setup..."
echo "===================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_setup() { echo -e "${PURPLE}🚀 $1${NC}"; }
log_phase() { echo -e "${CYAN}📋 $1${NC}"; }

# Function to check prerequisites
check_prerequisites() {
    log_phase "Phase 1: Checking Prerequisites"
    echo "==============================="
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed!"
        exit 1
    fi
    log_success "Docker is available"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed!"
        exit 1
    fi
    log_success "Docker Compose is available"
    
    # Check if docker-compose.yml exists
    if [[ ! -f "docker-compose.yml" ]]; then
        log_error "docker-compose.yml not found!"
        exit 1
    fi
    log_success "docker-compose.yml found"
    
    # Create necessary directories
    mkdir -p {logs,backups,config,data,scripts/recovery,plugins/SkyWars,database}
    log_success "Directory structure created"
}

# Function to prepare scripts
prepare_scripts() {
    log_phase "Phase 2: Preparing Enhancement Scripts"
    echo "======================================"
    
    # Make all scripts executable
    find scripts/ -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
    chmod +x *.sh 2>/dev/null || true
    
    # List of critical scripts to check
    local critical_scripts=(
        "deploy-enhanced-skywars.sh"
        "scripts/fix-skywars-system.sh"
        "scripts/error-handling-system.sh"
        "scripts/plugin-integration.sh"
        "scripts/validation-system.sh"
        "scripts/performance-monitoring.sh"
        "scripts/enhanced-database-operations.sh"
        "scripts/fallback-systems.sh"
        "scripts/recovery-procedures.sh"
        "scripts/command-registration.sh"
    )
    
    local missing_scripts=0
    for script in "${critical_scripts[@]}"; do
        if [[ -f "$script" ]]; then
            chmod +x "$script"
            log_success "Ready: $(basename $script)"
        else
            log_warning "Missing: $script"
            ((missing_scripts++))
        fi
    done
    
    if [[ $missing_scripts -gt 0 ]]; then
        log_warning "$missing_scripts enhancement scripts are missing"
        log_info "Will continue with available scripts"
    else
        log_success "All enhancement scripts are ready"
    fi
}

# Function to create and start containers
create_containers() {
    log_phase "Phase 3: Creating and Starting Containers"
    echo "=========================================="
    
    # Pull latest images
    log_setup "Pulling latest Docker images..."
    docker-compose pull
    log_success "Images pulled successfully"
    
    # Start containers
    log_setup "Starting containers..."
    docker-compose up -d
    log_success "Containers started"
    
    # Wait for containers to be ready
    log_setup "Waiting for containers to initialize..."
    sleep 30
    
    # Check container status
    log_info "Checking container status..."
    if docker-compose ps | grep -q "Up"; then
        log_success "Containers are running"
        docker-compose ps
    else
        log_error "Some containers failed to start"
        docker-compose ps
        return 1
    fi
}

# Function to wait for services
wait_for_services() {
    log_phase "Phase 4: Waiting for Services to be Ready"
    echo "=========================================="
    
    # Wait for Minecraft server
    log_setup "Waiting for Minecraft server to be ready..."
    local mc_attempts=0
    while [[ $mc_attempts -lt 120 ]]; do
        if docker-compose logs mc | grep -q "Done\|Timings Reset"; then
            log_success "Minecraft server is ready"
            break
        fi
        echo "   Server starting... ($((mc_attempts + 1))/120)"
        sleep 5
        ((mc_attempts++))
    done
    
    if [[ $mc_attempts -eq 120 ]]; then
        log_warning "Minecraft server took longer than expected to start"
        log_info "Continuing anyway..."
    fi
    
    # Wait for database
    log_setup "Waiting for database to be ready..."
    local db_attempts=0
    while [[ $db_attempts -lt 60 ]]; do
        if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
            log_success "Database is ready"
            break
        fi
        echo "   Database starting... ($((db_attempts + 1))/60)"
        sleep 3
        ((db_attempts++))
    done
    
    if [[ $db_attempts -eq 60 ]]; then
        log_warning "Database took longer than expected to start"
        log_info "Continuing anyway..."
    fi
    
    # Additional wait for stability
    log_setup "Allowing services to stabilize..."
    sleep 15
    log_success "Services are ready"
}

# Function to run enhancement scripts
run_enhancement_scripts() {
    log_phase "Phase 5: Running Enhancement Scripts"
    echo "==================================="
    
    # Define scripts in execution order
    local scripts_order=(
        "scripts/fix-skywars-system.sh"
        "scripts/error-handling-system.sh"
        "scripts/enhanced-database-operations.sh"
        "scripts/plugin-integration.sh"
        "scripts/fallback-systems.sh"
        "scripts/performance-monitoring.sh"
        "scripts/command-registration.sh"
        "scripts/recovery-procedures.sh"
    )
    
    local successful_scripts=0
    local total_scripts=${#scripts_order[@]}
    
    for script in "${scripts_order[@]}"; do
        if [[ -f "$script" ]]; then
            log_setup "Running: $(basename $script)"
            echo "----------------------------------------"
            
            if ./"$script"; then
                log_success "✅ Completed: $(basename $script)"
                ((successful_scripts++))
            else
                log_warning "⚠️  Completed with warnings: $(basename $script)"
                ((successful_scripts++))
            fi
            
            echo ""
            sleep 2
        else
            log_warning "Script not found: $script"
        fi
    done
    
    log_info "Enhancement scripts completed: $successful_scripts/$total_scripts"
    
    # Run master deployment if available
    if [[ -f "deploy-enhanced-skywars.sh" ]]; then
        log_setup "Running master deployment script..."
        echo "----------------------------------------"
        
        if ./deploy-enhanced-skywars.sh; then
            log_success "✅ Master deployment completed"
        else
            log_warning "⚠️  Master deployment completed with warnings"
        fi
    fi
}

# Function to validate setup
validate_setup() {
    log_phase "Phase 6: Validating Setup"
    echo "========================="
    
    # Run validation script
    if [[ -f "scripts/validation-system.sh" ]]; then
        log_setup "Running comprehensive validation..."
        echo "----------------------------------------"
        
        if ./scripts/validation-system.sh; then
            log_success "✅ System validation passed"
        else
            log_warning "⚠️  System validation found issues"
        fi
        echo ""
    fi
    
    # Run health assessment
    if [[ -f "scripts/master-recovery.sh" ]]; then
        log_setup "Running health assessment..."
        echo "----------------------------------------"
        
        if ./scripts/master-recovery.sh assess; then
            log_success "✅ Health assessment passed"
        else
            log_warning "⚠️  Health assessment found issues"
        fi
        echo ""
    fi
    
    # Test basic server functionality
    log_setup "Testing basic server functionality..."
    if docker exec minecraft-server-docker-mc-1 rcon-cli "list" >/dev/null 2>&1; then
        log_success "✅ Server responds to commands"
    else
        log_warning "⚠️  Server command test failed"
    fi
    
    # Test database connectivity
    log_setup "Testing database connectivity..."
    if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
        log_success "✅ Database is accessible"
    else
        log_warning "⚠️  Database connectivity test failed"
    fi
}

# Function to start monitoring
start_monitoring() {
    log_phase "Phase 7: Starting Monitoring Systems"
    echo "===================================="
    
    # Start performance monitoring
    if [[ -f "scripts/continuous-performance-monitor.sh" ]]; then
        log_setup "Starting continuous performance monitoring..."
        ./scripts/continuous-performance-monitor.sh start
        log_success "✅ Performance monitoring started"
    fi
    
    # Create initial performance baseline
    if [[ -f "scripts/system-performance-monitor.sh" ]]; then
        log_setup "Creating performance baseline..."
        ./scripts/system-performance-monitor.sh > logs/initial-performance-baseline.log 2>&1
        log_success "✅ Performance baseline created"
    fi
}

# Function to setup basic permissions
setup_permissions() {
    log_phase "Phase 8: Setting Up Basic Permissions"
    echo "====================================="
    
    log_setup "Setting up default SkyWars permissions..."
    
    # Give basic permissions to default group
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.use true" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.join true" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.leave true" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.stats true" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group default permission set skywars.player.help true" 2>/dev/null || true
    
    log_success "✅ Basic permissions configured"
    
    # Create admin group
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp creategroup skywars_admin" 2>/dev/null || true
    docker exec minecraft-server-docker-mc-1 rcon-cli "lp group skywars_admin permission set skywars.* true" 2>/dev/null || true
    
    log_success "✅ Admin group created"
    
    log_info "To give admin permissions to a player, use:"
    log_info "docker exec minecraft-server-docker-mc-1 rcon-cli \"lp user <username> parent add skywars_admin\""
}

# Function to display final status
display_final_status() {
    log_phase "Phase 9: Final Status Report"
    echo "============================"
    
    echo ""
    log_success "🎉 Complete SkyWars Setup Finished!"
    echo ""
    
    # Container status
    echo "🐳 Container Status:"
    echo "==================="
    docker-compose ps
    echo ""
    
    # Service endpoints
    echo "🌐 Service Endpoints:"
    echo "===================="
    echo "• Minecraft Server: localhost:25565"
    echo "• File Manager: http://localhost:8080"
    echo "• Database: localhost:3306"
    echo "• RCON: localhost:25575"
    echo ""
    
    # Available commands
    echo "🎮 SkyWars Commands:"
    echo "==================="
    echo "• /sw help - Show help"
    echo "• /sw join [solo|doubles] - Join game"
    echo "• /sw leave - Leave game"
    echo "• /sw stats - View statistics"
    echo "• /sw top - View leaderboards"
    echo ""
    
    # Management commands
    echo "🔧 Management Commands:"
    echo "======================"
    echo "• ./scripts/master-recovery.sh assess - Health check"
    echo "• ./scripts/system-performance-monitor.sh - Performance check"
    echo "• ./scripts/continuous-performance-monitor.sh status - Monitoring status"
    echo "• docker-compose logs -f mc - View server logs"
    echo ""
    
    # Next steps
    echo "📋 Next Steps:"
    echo "=============="
    echo "1. Connect to server: localhost:25565"
    echo "2. Test SkyWars: /sw help"
    echo "3. Give admin permissions: lp user <username> parent add skywars_admin"
    echo "4. Monitor performance: ./scripts/system-performance-monitor.sh"
    echo "5. Check logs: docker-compose logs -f mc"
    echo ""
    
    log_success "🚀 Your enhanced SkyWars server is ready for epic battles!"
}

# Main execution function
main() {
    log_info "Starting complete SkyWars setup process..."
    echo ""
    
    # Execute all phases
    check_prerequisites
    echo ""
    
    prepare_scripts
    echo ""
    
    create_containers
    echo ""
    
    wait_for_services
    echo ""
    
    run_enhancement_scripts
    echo ""
    
    validate_setup
    echo ""
    
    start_monitoring
    echo ""
    
    setup_permissions
    echo ""
    
    display_final_status
    
    echo ""
    log_success "🎉 Complete setup process finished successfully!"
    echo ""
    echo "Your SkyWars server is now running with all enhancements!"
    echo "Connect to localhost:25565 and start playing! 🎮⚔️🏆"
}

# Run main function
main "$@"
