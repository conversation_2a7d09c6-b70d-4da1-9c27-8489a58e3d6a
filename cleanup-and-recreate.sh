#!/bin/bash

# 🧹 Complete SkyWars System Cleanup and Recreation
# Stops all containers, cleans data, and recreates with enhancements
# Version: 2.1 - Complete Reset and Enhancement Application

set -e

echo "🧹 Starting Complete SkyWars System Cleanup and Recreation..."
echo "============================================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_cleanup() { echo -e "${PURPLE}🧹 $1${NC}"; }
log_phase() { echo -e "${CYAN}📋 $1${NC}"; }

# Function to check if Docker is running
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH!"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running!"
        exit 1
    fi
    
    log_success "Docker is available and running"
}

# Function to backup current system
backup_current_system() {
    log_phase "Phase 1: Backing Up Current System"
    echo "=================================="
    
    local backup_dir="backups/pre-cleanup-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup configurations
    if [[ -d "plugins" ]]; then
        cp -r plugins "$backup_dir/" 2>/dev/null || true
        log_success "Plugin configurations backed up"
    fi
    
    # Backup worlds
    for world in lobby modern_skywar world world_nether world_the_end; do
        if [[ -d "$world" ]]; then
            cp -r "$world" "$backup_dir/" 2>/dev/null || true
            log_success "World '$world' backed up"
        fi
    done
    
    # Backup database data
    if [[ -d "mysql-data" ]]; then
        cp -r mysql-data "$backup_dir/" 2>/dev/null || true
        log_success "Database data backed up"
    fi
    
    # Backup docker-compose and other configs
    for file in docker-compose.yml .env server.properties; do
        if [[ -f "$file" ]]; then
            cp "$file" "$backup_dir/" 2>/dev/null || true
        fi
    done
    
    log_success "System backup completed: $backup_dir"
}

# Function to stop and remove all containers
stop_and_remove_containers() {
    log_phase "Phase 2: Stopping and Removing Containers"
    echo "=========================================="
    
    # Stop all containers gracefully
    log_cleanup "Stopping all containers gracefully..."
    docker-compose down --timeout 30 2>/dev/null || true
    
    # Force stop any remaining containers
    log_cleanup "Force stopping any remaining containers..."
    docker stop $(docker ps -aq) 2>/dev/null || true
    
    # Remove all containers
    log_cleanup "Removing all containers..."
    docker rm $(docker ps -aq) 2>/dev/null || true
    
    # Remove all volumes
    log_cleanup "Removing all volumes..."
    docker-compose down -v 2>/dev/null || true
    docker volume prune -f 2>/dev/null || true
    
    log_success "All containers and volumes removed"
}

# Function to clean up data directories
cleanup_data_directories() {
    log_phase "Phase 3: Cleaning Up Data Directories"
    echo "====================================="
    
    # List of directories to clean
    local data_dirs=(
        "mysql-data"
        "files/logs"
        "files/crash-reports"
        "files/debug"
        "logs"
        "temp"
        ".cache"
    )
    
    for dir in "${data_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            log_cleanup "Cleaning directory: $dir"
            rm -rf "$dir"
            log_success "Cleaned: $dir"
        fi
    done
    
    # Clean specific files
    local files_to_clean=(
        "files/server.log"
        "files/latest.log"
        "files/usercache.json"
        "files/banned-ips.json"
        "files/banned-players.json"
        "files/ops.json"
        "files/whitelist.json"
    )
    
    for file in "${files_to_clean[@]}"; do
        if [[ -f "$file" ]]; then
            log_cleanup "Removing file: $file"
            rm -f "$file"
            log_success "Removed: $file"
        fi
    done
    
    log_success "Data cleanup completed"
}

# Function to clean Docker system
cleanup_docker_system() {
    log_phase "Phase 4: Docker System Cleanup"
    echo "=============================="
    
    # Remove unused images
    log_cleanup "Removing unused Docker images..."
    docker image prune -f 2>/dev/null || true
    
    # Remove unused networks
    log_cleanup "Removing unused Docker networks..."
    docker network prune -f 2>/dev/null || true
    
    # Remove build cache
    log_cleanup "Removing Docker build cache..."
    docker builder prune -f 2>/dev/null || true
    
    # System-wide cleanup
    log_cleanup "Running Docker system cleanup..."
    docker system prune -f 2>/dev/null || true
    
    log_success "Docker system cleanup completed"
}

# Function to prepare enhanced system
prepare_enhanced_system() {
    log_phase "Phase 5: Preparing Enhanced System"
    echo "=================================="
    
    # Create necessary directories
    log_info "Creating directory structure..."
    mkdir -p {logs,backups,config,data,scripts/recovery,plugins/SkyWars,database}
    
    # Set proper permissions
    log_info "Setting proper permissions..."
    chmod -R 755 scripts/ 2>/dev/null || true
    find scripts/ -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
    
    # Ensure all enhancement scripts are executable
    local enhancement_scripts=(
        "deploy-enhanced-skywars.sh"
        "scripts/fix-skywars-system.sh"
        "scripts/error-handling-system.sh"
        "scripts/plugin-integration.sh"
        "scripts/validation-system.sh"
        "scripts/performance-monitoring.sh"
        "scripts/enhanced-database-operations.sh"
        "scripts/fallback-systems.sh"
        "scripts/recovery-procedures.sh"
        "scripts/command-registration.sh"
        "scripts/master-recovery.sh"
    )
    
    for script in "${enhancement_scripts[@]}"; do
        if [[ -f "$script" ]]; then
            chmod +x "$script"
            log_success "Made executable: $script"
        else
            log_warning "Enhancement script not found: $script"
        fi
    done
    
    log_success "Enhanced system preparation completed"
}

# Function to recreate containers with enhancements
recreate_containers() {
    log_phase "Phase 6: Recreating Containers with Enhancements"
    echo "=============================================="
    
    # Pull latest images
    log_info "Pulling latest Docker images..."
    docker-compose pull
    
    # Start containers
    log_info "Starting containers..."
    docker-compose up -d
    
    # Wait for containers to be ready
    log_info "Waiting for containers to start..."
    sleep 30
    
    # Check container status
    log_info "Checking container status..."
    if docker-compose ps | grep -q "Up"; then
        log_success "Containers are running"
    else
        log_error "Some containers failed to start"
        docker-compose ps
        return 1
    fi
    
    # Wait for Minecraft server to be ready
    log_info "Waiting for Minecraft server to be ready..."
    local attempts=0
    while [[ $attempts -lt 60 ]]; do
        if docker-compose logs mc | grep -q "Done\|Timings Reset"; then
            log_success "Minecraft server is ready"
            break
        fi
        echo "   Server starting... ($((attempts + 1))/60)"
        sleep 5
        ((attempts++))
    done
    
    if [[ $attempts -eq 60 ]]; then
        log_warning "Server took longer than expected to start"
    fi
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    attempts=0
    while [[ $attempts -lt 30 ]]; do
        if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
            log_success "Database is ready"
            break
        fi
        echo "   Database starting... ($((attempts + 1))/30)"
        sleep 3
        ((attempts++))
    done
    
    if [[ $attempts -eq 30 ]]; then
        log_warning "Database took longer than expected to start"
    fi
    
    log_success "Container recreation completed"
}

# Function to apply enhancements
apply_enhancements() {
    log_phase "Phase 7: Applying SkyWars Enhancements"
    echo "====================================="
    
    # Run the enhanced deployment script
    if [[ -f "deploy-enhanced-skywars.sh" ]]; then
        log_info "Running enhanced SkyWars deployment..."
        if ./deploy-enhanced-skywars.sh; then
            log_success "Enhanced SkyWars deployment completed"
        else
            log_warning "Enhanced deployment completed with warnings"
        fi
    else
        log_warning "Enhanced deployment script not found, running individual components..."
        
        # Run individual enhancement scripts
        local scripts=(
            "scripts/fix-skywars-system.sh"
            "scripts/error-handling-system.sh"
            "scripts/plugin-integration.sh"
            "scripts/enhanced-database-operations.sh"
            "scripts/performance-monitoring.sh"
            "scripts/command-registration.sh"
        )
        
        for script in "${scripts[@]}"; do
            if [[ -f "$script" ]]; then
                log_info "Running: $script"
                if ./"$script"; then
                    log_success "Completed: $script"
                else
                    log_warning "Completed with warnings: $script"
                fi
            fi
        done
    fi
    
    log_success "Enhancement application completed"
}

# Function to validate new system
validate_new_system() {
    log_phase "Phase 8: Validating New System"
    echo "=============================="
    
    # Run validation script
    if [[ -f "scripts/validation-system.sh" ]]; then
        log_info "Running comprehensive system validation..."
        if ./scripts/validation-system.sh; then
            log_success "System validation passed"
        else
            log_warning "System validation found issues"
        fi
    fi
    
    # Run health assessment
    if [[ -f "scripts/master-recovery.sh" ]]; then
        log_info "Running health assessment..."
        if ./scripts/master-recovery.sh assess; then
            log_success "Health assessment passed"
        else
            log_warning "Health assessment found issues"
        fi
    fi
    
    # Start monitoring
    if [[ -f "scripts/continuous-performance-monitor.sh" ]]; then
        log_info "Starting performance monitoring..."
        ./scripts/continuous-performance-monitor.sh start
        log_success "Performance monitoring started"
    fi
    
    log_success "System validation completed"
}

# Function to display final status
display_final_status() {
    log_phase "Phase 9: Final Status Report"
    echo "============================"
    
    echo ""
    log_success "🎉 Complete SkyWars System Recreation Finished!"
    echo ""
    echo "📊 System Status:"
    echo "================"
    
    # Check container status
    echo "🐳 Container Status:"
    docker-compose ps
    echo ""
    
    # Check disk usage
    echo "💾 Disk Usage:"
    df -h . | tail -1
    echo ""
    
    # Check Docker usage
    echo "🐋 Docker Usage:"
    docker system df
    echo ""
    
    echo "✅ What's Been Accomplished:"
    echo "============================"
    echo "• ✅ Complete system backup created"
    echo "• ✅ All containers stopped and removed"
    echo "• ✅ Data directories cleaned"
    echo "• ✅ Docker system cleaned"
    echo "• ✅ Enhanced system prepared"
    echo "• ✅ Containers recreated with fresh data"
    echo "• ✅ SkyWars enhancements applied"
    echo "• ✅ System validation completed"
    echo "• ✅ Performance monitoring started"
    echo ""
    
    echo "🎮 Ready for Use:"
    echo "================"
    echo "• Server: http://localhost:25565"
    echo "• File Manager: http://localhost:8080"
    echo "• Database: localhost:3306"
    echo ""
    
    echo "🔧 Next Steps:"
    echo "=============="
    echo "1. Test server connectivity"
    echo "2. Create player accounts"
    echo "3. Test SkyWars commands (/sw help)"
    echo "4. Monitor system performance"
    echo "5. Configure additional settings as needed"
    echo ""
    
    echo "📋 Monitoring Commands:"
    echo "======================"
    echo "• System health: ./scripts/master-recovery.sh assess"
    echo "• Performance: ./scripts/system-performance-monitor.sh"
    echo "• Logs: docker-compose logs -f mc"
    echo "• Monitoring status: ./scripts/continuous-performance-monitor.sh status"
    echo ""
    
    log_success "🚀 Your enhanced SkyWars server is ready!"
}

# Main execution function
main() {
    echo "⚠️  WARNING: This will completely reset your Minecraft server!"
    echo "All current data will be backed up but containers will be recreated."
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " confirm
    
    if [[ "$confirm" != "yes" ]]; then
        echo "Operation cancelled."
        exit 0
    fi
    
    echo ""
    log_info "Starting complete system recreation..."
    
    # Execute all phases
    check_docker
    backup_current_system
    stop_and_remove_containers
    cleanup_data_directories
    cleanup_docker_system
    prepare_enhanced_system
    recreate_containers
    apply_enhancements
    validate_new_system
    display_final_status
    
    echo ""
    log_success "🎉 Complete system recreation finished successfully!"
    echo ""
    echo "Your SkyWars server has been completely recreated with all enhancements!"
    echo "The system is now running with fresh data and all improvements applied."
}

# Run main function
main "$@"
