# 🎯 Professional SkyWars Queue Management System
# Advanced queue system for optimal player experience
# Version: 2.0 - Professional Edition

# 🎮 Queue Configuration
queue:
  # Global queue settings
  global:
    enabled: true
    max-queue-size: 1000
    queue-timeout: 300  # 5 minutes
    auto-cleanup: true
    cleanup-interval: 60  # 1 minute
    
  # Queue types
  types:
    solo:
      enabled: true
      name: "&6Solo SkyWars"
      description: "&7Individual battles for glory!"
      icon: "DIAMOND_SWORD"
      max-players: 500
      priority: 1
      
    doubles:
      enabled: true
      name: "&bDoubles SkyWars"
      description: "&7Team up with a friend!"
      icon: "IRON_SWORD"
      max-players: 300
      priority: 2
      
    squads:
      enabled: false
      name: "&cSquads SkyWars"
      description: "&7Form a squad of 4!"
      icon: "GOLDEN_SWORD"
      max-players: 200
      priority: 3
      
    ranked:
      enabled: false
      name: "&5Ranked SkyWars"
      description: "&7Competitive matches!"
      icon: "NETHERITE_SWORD"
      max-players: 100
      priority: 4
      skill-based: true
      min-level: 10

# ⏱️ Matchmaking System
matchmaking:
  # Basic matchmaking
  basic:
    enabled: true
    min-players: 2
    optimal-players: 8
    max-wait-time: 120  # 2 minutes
    force-start-time: 180  # 3 minutes
    
  # Smart matchmaking
  smart:
    enabled: true
    skill-based: false
    ping-based: true
    party-priority: true
    balance-teams: true
    
  # Advanced features
  advanced:
    backfill: true  # Fill games that lost players
    cross-server: false  # Multi-server support
    priority-queue: true  # VIP queue
    anti-snipe: true  # Prevent stream sniping

# 👥 Party System
party:
  enabled: true
  max-size: 4
  auto-balance: true
  leader-controls: true
  
  # Party features
  features:
    chat: true
    teleport-together: true
    shared-queue: true
    leader-decisions: true
    
  # Party restrictions
  restrictions:
    same-game-mode: true
    level-difference: 20  # Max level difference
    rank-difference: 2   # Max rank difference

# 🏆 Skill-Based Matchmaking (for ranked)
skill-matching:
  enabled: false  # Enable for ranked mode
  
  # Rating system
  rating:
    initial: 1000
    min: 0
    max: 3000
    k-factor: 32  # ELO calculation factor
    
  # Matchmaking ranges
  ranges:
    bronze: {min: 0, max: 999, name: "&8Bronze", color: "&8"}
    silver: {min: 1000, max: 1499, name: "&7Silver", color: "&7"}
    gold: {min: 1500, max: 1999, name: "&6Gold", color: "&6"}
    platinum: {min: 2000, max: 2499, name: "&bPlatinum", color: "&b"}
    diamond: {min: 2500, max: 2999, name: "&3Diamond", color: "&3"}
    master: {min: 3000, max: 9999, name: "&5Master", color: "&5"}
    
  # Matching tolerance
  tolerance:
    initial: 100
    max: 500
    increase-rate: 50  # Per 30 seconds
    
# 🎨 Queue Interface
interface:
  # Lobby NPCs
  npcs:
    enabled: true
    locations:
      solo: {world: "lobby", x: 0, y: 69, z: 25}
      doubles: {world: "lobby", x: 5, y: 69, z: 25}
      squads: {world: "lobby", x: -5, y: 69, z: 25}
    
    # NPC configuration
    config:
      skin: "SkyWars_NPC"
      name-format: "&b&lSkyWars %mode%"
      click-message: "&aClick to join %mode% queue!"
      
  # Signs
  signs:
    enabled: true
    auto-update: true
    update-interval: 5  # seconds
    
    # Sign format
    format:
      line1: "&b&lSkyWars"
      line2: "&e%mode%"
      line3: "&7Players: &a%players%"
      line4: "&7Click to join!"
      
  # Holograms
  holograms:
    enabled: true
    locations:
      main: {world: "lobby", x: 0, y: 72, z: 25}
      
    # Hologram content
    content:
      - "&b&l🏆 SkyWars Statistics"
      - "&7Players Online: &e%online%"
      - "&7Games Today: &e%games_today%"
      - "&7Top Player: &6%top_player%"
      - ""
      - "&eClick NPCs below to join!"

# 📊 Queue Analytics
analytics:
  enabled: true
  
  # Tracked metrics
  metrics:
    queue-times: true
    match-quality: true
    player-satisfaction: true
    server-performance: true
    
  # Data retention
  retention:
    detailed: 7   # days
    summary: 30   # days
    
# 🔧 Performance Optimization
performance:
  # Queue processing
  processing:
    async: true
    batch-size: 50
    process-interval: 1  # second
    
  # Memory management
  memory:
    cache-size: 1000
    cleanup-interval: 300  # 5 minutes
    
  # Database optimization
  database:
    batch-updates: true
    connection-pool: 10
    query-timeout: 5  # seconds

# 🎯 Priority System
priority:
  enabled: true
  
  # Priority levels
  levels:
    vip: {priority: 10, name: "&6VIP", bypass-queue: false}
    premium: {priority: 20, name: "&bPremium", bypass-queue: false}
    staff: {priority: 100, name: "&cStaff", bypass-queue: true}
    
  # Priority features
  features:
    reduced-wait-time: true
    priority-matching: true
    queue-skip: false  # Only for highest priority

# 🚫 Anti-Abuse System
anti-abuse:
  enabled: true
  
  # Queue dodging prevention
  queue-dodging:
    enabled: true
    penalty-time: 300  # 5 minutes
    max-dodges: 3
    reset-time: 3600  # 1 hour
    
  # Spam prevention
  spam-prevention:
    enabled: true
    max-joins-per-minute: 10
    cooldown: 5  # seconds
    
  # Alt account detection
  alt-detection:
    enabled: true
    same-ip-limit: 3
    suspicious-behavior: true

# 📱 Notifications
notifications:
  # Queue updates
  queue-updates:
    position-change: true
    estimated-time: true
    match-found: true
    
  # Match notifications
  match:
    found: true
    starting: true
    cancelled: true
    
  # System notifications
  system:
    maintenance: true
    updates: true
    events: true

# 🎮 Game Mode Specific Settings
game-modes:
  solo:
    queue-size: 12
    start-threshold: 8
    force-start: 6
    
  doubles:
    queue-size: 16
    start-threshold: 12
    force-start: 8
    team-balance: true
    
  squads:
    queue-size: 32
    start-threshold: 24
    force-start: 16
    team-balance: true
    
  ranked:
    queue-size: 12
    start-threshold: 10
    force-start: 8
    skill-matching: true
    placement-matches: 10

# 🔄 Queue States
states:
  # Player states
  player:
    LOBBY: "Player in main lobby"
    QUEUED: "Player waiting in queue"
    MATCHED: "Player matched, waiting for game"
    INGAME: "Player in active game"
    SPECTATING: "Player spectating game"
    
  # Queue states
  queue:
    OPEN: "Queue accepting players"
    FULL: "Queue at maximum capacity"
    CLOSED: "Queue temporarily closed"
    MAINTENANCE: "Queue under maintenance"

# 🎨 Customization
customization:
  # Messages
  messages:
    use-custom: true
    file: "queue-messages.yml"
    
  # Sounds
  sounds:
    queue-join: "ENTITY_EXPERIENCE_ORB_PICKUP"
    queue-leave: "ENTITY_VILLAGER_NO"
    match-found: "ENTITY_PLAYER_LEVELUP"
    position-update: "BLOCK_NOTE_BLOCK_PLING"
    
  # Effects
  effects:
    queue-join: "VILLAGER_HAPPY"
    match-found: "FIREWORKS_SPARK"
    teleport: "PORTAL"
