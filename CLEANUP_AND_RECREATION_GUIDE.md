# 🧹 Complete SkyWars System Cleanup and Recreation Guide

## 🚨 **IMPORTANT: Complete System Reset**

This guide will help you completely stop, clean, and recreate your Minecraft server containers with all the SkyWars enhancements applied. **This will reset all data but apply all improvements.**

## 📋 **What This Process Does**

### ✅ **Cleanup Actions**
1. **Backup Current System** - Creates timestamped backup of all data
2. **Stop All Containers** - Gracefully stops Minecraft server and database
3. **Remove Containers & Volumes** - Completely removes all Docker containers and volumes
4. **Clean Data Directories** - Removes logs, cache, and temporary files
5. **Docker System Cleanup** - Removes unused images, networks, and build cache

### ✅ **Recreation Actions**
1. **Prepare Enhanced System** - Sets up directory structure and permissions
2. **Recreate Containers** - Starts fresh containers with latest images
3. **Apply SkyWars Enhancements** - Deploys all 22 completed enhancements
4. **Validate System** - Runs comprehensive validation and health checks
5. **Start Monitoring** - Activates performance monitoring and alerting

## 🚀 **Step-by-Step Instructions**

### **Step 1: Prepare for Cleanup**
```bash
# Navigate to your Minecraft server directory
cd /path/to/minecraft-server-docker

# Make the cleanup script executable
chmod +x cleanup-and-recreate.sh

# Optional: Review what will be cleaned
cat cleanup-and-recreate.sh
```

### **Step 2: Run Complete Cleanup and Recreation**
```bash
# Run the complete cleanup and recreation process
./cleanup-and-recreate.sh
```

**The script will:**
- Ask for confirmation before proceeding
- Show progress for each phase
- Create automatic backups
- Handle any errors gracefully

### **Step 3: Monitor the Process**
The script will show progress through these phases:
1. 📋 **Phase 1**: Backing Up Current System
2. 🧹 **Phase 2**: Stopping and Removing Containers  
3. 🧹 **Phase 3**: Cleaning Up Data Directories
4. 🧹 **Phase 4**: Docker System Cleanup
5. 📋 **Phase 5**: Preparing Enhanced System
6. 📋 **Phase 6**: Recreating Containers with Enhancements
7. 📋 **Phase 7**: Applying SkyWars Enhancements
8. 📋 **Phase 8**: Validating New System
9. 📋 **Phase 9**: Final Status Report

## ⚠️ **Manual Steps (If Docker Issues)**

If Docker is not available or you prefer manual control:

### **Manual Cleanup**
```bash
# Stop containers
docker-compose down -v

# Remove all containers
docker stop $(docker ps -aq)
docker rm $(docker ps -aq)

# Remove volumes
docker volume prune -f

# Clean system
docker system prune -f

# Remove data directories
rm -rf mysql-data logs files/logs files/crash-reports
```

### **Manual Recreation**
```bash
# Pull latest images
docker-compose pull

# Start containers
docker-compose up -d

# Wait for startup (check logs)
docker-compose logs -f mc

# Apply enhancements
./deploy-enhanced-skywars.sh
```

## 📊 **Expected Results**

### **After Successful Completion**
- ✅ Fresh Minecraft server with clean data
- ✅ All SkyWars enhancements applied
- ✅ Performance monitoring active
- ✅ Error handling and recovery systems operational
- ✅ Database optimized and configured
- ✅ Plugin integration layer functional

### **System Status Check**
```bash
# Check container status
docker-compose ps

# Check system health
./scripts/master-recovery.sh assess

# Check performance
./scripts/system-performance-monitor.sh

# View monitoring status
./scripts/continuous-performance-monitor.sh status
```

## 🎮 **Testing Your New System**

### **Basic Functionality Test**
```bash
# Connect to server (Minecraft client)
# Server: localhost:25565

# Test SkyWars commands in-game
/sw help
/sw join solo
/sw stats
/sw leave
```

### **Admin Testing**
```bash
# Give yourself admin permissions
docker exec minecraft-server-docker-mc-1 rcon-cli "lp user YourUsername parent add skywars_admin"

# Test admin commands
/sw admin
/sw arena list
/sw game status
```

## 🔧 **Post-Recreation Configuration**

### **Essential Setup**
1. **Create Admin Account**
   ```bash
   docker exec minecraft-server-docker-mc-1 rcon-cli "lp user YourUsername parent add skywars_admin"
   ```

2. **Configure Server Settings**
   - Edit `files/server.properties` if needed
   - Restart server: `docker-compose restart mc`

3. **Test SkyWars Features**
   - Join games with `/sw join`
   - Check statistics with `/sw stats`
   - Test spectator mode with `/sw spectate`

### **Monitoring Setup**
1. **Start Continuous Monitoring**
   ```bash
   ./scripts/continuous-performance-monitor.sh start
   ```

2. **Configure Alerts** (Optional)
   - Edit `scripts/performance-alerting.sh`
   - Add Discord webhook for alerts
   - Set email notifications

3. **Review Logs**
   ```bash
   # Server logs
   docker-compose logs -f mc
   
   # Performance logs
   tail -f logs/performance-daemon.log
   
   # SkyWars logs
   tail -f logs/skywars-performance-*.log
   ```

## 🚨 **Troubleshooting**

### **If Containers Don't Start**
```bash
# Check Docker status
docker info

# Check logs
docker-compose logs

# Try manual restart
docker-compose down
docker-compose up -d
```

### **If Database Issues**
```bash
# Run database recovery
./scripts/recovery/database-recovery.sh

# Or reset database
docker-compose down -v
docker-compose up -d db
```

### **If SkyWars Features Don't Work**
```bash
# Run system validation
./scripts/validation-system.sh

# Check plugin status
docker exec minecraft-server-docker-mc-1 rcon-cli "plugins"

# Run recovery
./scripts/master-recovery.sh recover all
```

## 📋 **Backup Information**

### **Automatic Backups Created**
- **Location**: `backups/pre-cleanup-YYYYMMDD_HHMMSS/`
- **Contents**: 
  - Plugin configurations
  - World data
  - Database data
  - Server configurations

### **Restore from Backup** (If Needed)
```bash
# List available backups
ls -la backups/

# Restore specific backup
cp -r backups/pre-cleanup-YYYYMMDD_HHMMSS/* .

# Restart containers
docker-compose restart
```

## ✅ **Success Indicators**

### **System is Ready When:**
- ✅ All containers show "Up" status
- ✅ Minecraft server logs show "Done" or "Timings Reset"
- ✅ Database responds to ping
- ✅ SkyWars commands work in-game
- ✅ Performance monitoring is active
- ✅ Health checks pass

### **Performance Metrics**
- **TPS**: Should be 20.0 (or close)
- **Memory**: Should be under 80%
- **Response Time**: Should be under 100ms
- **Database**: Should respond quickly

## 🎯 **Final Verification**

Run this complete verification:
```bash
# 1. Check all systems
./scripts/master-recovery.sh assess

# 2. Run performance check
./scripts/system-performance-monitor.sh

# 3. Validate configuration
./scripts/validation-system.sh

# 4. Test SkyWars functionality
# (Connect to server and test /sw commands)

# 5. Check monitoring
./scripts/continuous-performance-monitor.sh status
```

## 🏆 **You're Done!**

Your Minecraft server has been completely recreated with:
- ✅ Fresh, clean data
- ✅ All 22 SkyWars enhancements applied
- ✅ Professional-grade reliability and monitoring
- ✅ Comprehensive error handling and recovery
- ✅ Performance optimization and alerting

**Your enhanced SkyWars server is now ready for epic battles!** 🎮⚔️🏆

---

**Need Help?** 
- Check logs: `docker-compose logs -f mc`
- Run health check: `./scripts/master-recovery.sh assess`
- View this guide: `cat CLEANUP_AND_RECREATION_GUIDE.md`
