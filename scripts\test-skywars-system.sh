#!/bin/bash

# 🧪 Professional SkyWars System Testing Script
# Comprehensive testing of all match management features
# Version: 2.0 - Professional Edition

echo "🧪 Testing Professional SkyWars System..."
echo "=========================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_test() { echo -e "${PURPLE}🧪 $1${NC}"; }

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Function to execute RCON command and capture output
execute_command() {
    local cmd="$1"
    local expected="$2"
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    echo "Testing: $cmd"
    result=$(docker exec minecraft-server-docker-mc-1 rcon-cli "$cmd" 2>&1)
    
    if [[ -n "$expected" ]]; then
        if echo "$result" | grep -q "$expected"; then
            log_success "Test passed: $cmd"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            return 0
        else
            log_error "Test failed: $cmd"
            echo "Expected: $expected"
            echo "Got: $result"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            return 1
        fi
    else
        log_success "Command executed: $cmd"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    fi
}

# Function to test file existence
test_file_exists() {
    local file="$1"
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if [[ -f "$file" ]]; then
        log_success "File exists: $file"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "File missing: $file"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to test directory existence
test_dir_exists() {
    local dir="$1"
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if [[ -d "$dir" ]]; then
        log_success "Directory exists: $dir"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "Directory missing: $dir"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Check prerequisites
log_info "Checking prerequisites..."

# Check if server is running
if ! docker ps | grep -q minecraft-server-docker-mc-1; then
    log_error "Minecraft server container is not running!"
    exit 1
fi
log_success "Server container is running"

# Check if database is running
if ! docker ps | grep -q minecraft-server-docker-db-1; then
    log_error "Database container is not running!"
    exit 1
fi
log_success "Database container is running"

# 📁 Test Configuration Files
log_test "Testing configuration files..."

test_file_exists "plugins/SkyWars/main-config.yml"
test_file_exists "plugins/SkyWars/arenas.yml"
test_file_exists "plugins/SkyWars/messages.yml"
test_file_exists "plugins/SkyWars/chests.yml"
test_file_exists "plugins/SkyWars/queue-config.yml"
test_file_exists "plugins/SkyWars/timing-config.yml"
test_file_exists "plugins/SkyWars/player-management.yml"
test_file_exists "plugins/SkyWars/statistics-config.yml"
test_file_exists "plugins/SkyWars/spectator-config.yml"
test_file_exists "plugins/SkyWars/admin-config.yml"

# 🗄️ Test Database Connection
log_test "Testing database connection..."

# Test database connectivity
docker exec minecraft-server-docker-db-1 mysql -u hamza -p"Hh@#2021" minecraft-abusaker -e "SELECT 1;" > /dev/null 2>&1
if [[ $? -eq 0 ]]; then
    log_success "Database connection successful"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    log_error "Database connection failed"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi
TESTS_TOTAL=$((TESTS_TOTAL + 1))

# Test SkyWars tables
log_test "Testing SkyWars database tables..."

tables=("skywars_players" "skywars_matches" "skywars_match_participants" "skywars_achievements" "skywars_player_achievements" "skywars_leaderboards")

for table in "${tables[@]}"; do
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    if docker exec minecraft-server-docker-db-1 mysql -u hamza -p"Hh@#2021" minecraft-abusaker -e "DESCRIBE $table;" > /dev/null 2>&1; then
        log_success "Table exists: $table"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        log_error "Table missing: $table"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
done

# 🌍 Test World and Arena Setup
log_test "Testing world and arena setup..."

# Test lobby world
execute_command "mv list" "lobby"

# Test modern_skywar world
execute_command "mv list" "modern_skywar"

# Test if players can teleport to lobby
execute_command "mv tp console lobby"

# Test if SkyWars area exists
execute_command "tp console 0 65 25"

# 🎮 Test Basic SkyWars Commands
log_test "Testing basic SkyWars commands..."

# Test help command
execute_command "sw help"

# Test stats command (should work even with no data)
execute_command "sw stats console"

# Test arena list
execute_command "sw arenas"

# Test queue status
execute_command "sw queue"

# 🎯 Test Queue System
log_test "Testing queue system..."

# Test joining queue
execute_command "sw join solo"

# Test queue status after joining
execute_command "sw queue"

# Test leaving queue
execute_command "sw leave"

# 🏟️ Test Arena Management
log_test "Testing arena management..."

# Test arena information
execute_command "sw arena info modern_solo"

# Test arena status
execute_command "sw arena status"

# 📊 Test Statistics System
log_test "Testing statistics system..."

# Test leaderboard commands
execute_command "sw top wins"
execute_command "sw top kills"
execute_command "sw top kdr"

# Test personal stats
execute_command "sw stats console"

# 👻 Test Spectator System
log_test "Testing spectator system..."

# Test spectator commands
execute_command "sw spectate list"

# 🔧 Test Admin Commands (if permissions allow)
log_test "Testing admin commands..."

# Test admin help
execute_command "sw admin help"

# Test system status
execute_command "sw admin status"

# Test configuration reload
execute_command "sw reload"

# 🎨 Test UI Elements
log_test "Testing UI elements..."

# Check if SkyWars area has proper blocks
execute_command "testforblock 0 64 25 beacon"
execute_command "testforblock -2 65 24 oak_sign"
execute_command "testforblock 2 65 24 oak_sign"

# 🔄 Test Performance
log_test "Testing performance..."

# Test TPS
tps_result=$(execute_command "tps")
if echo "$tps_result" | grep -q "20.0\|19."; then
    log_success "Server TPS is good"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    log_warning "Server TPS may be low: $tps_result"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi
TESTS_TOTAL=$((TESTS_TOTAL + 1))

# Test memory usage
execute_command "memory"

# 🎪 Test Integration
log_test "Testing plugin integration..."

# Test AuthMe integration
execute_command "authme version"

# Test Essentials integration
execute_command "essentials version"

# Test Multiverse integration
execute_command "mv version"

# 🚨 Test Error Handling
log_test "Testing error handling..."

# Test invalid commands
execute_command "sw invalidcommand" "Unknown command"

# Test invalid arena
execute_command "sw join invalidarena" "not found"

# Test invalid player
execute_command "sw stats invalidplayer" "not found"

# 🔍 Test Edge Cases
log_test "Testing edge cases..."

# Test joining queue while already in queue
execute_command "sw join solo"
execute_command "sw join solo" "already"
execute_command "sw leave"

# Test leaving queue when not in queue
execute_command "sw leave" "not in queue"

# 📱 Test Mobile/Cross-Platform Compatibility
log_test "Testing cross-platform features..."

# Test if Geyser is available (for Bedrock support)
if docker exec minecraft-server-docker-mc-1 ls plugins/ | grep -q "Geyser"; then
    log_success "Geyser plugin detected - Bedrock support available"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    log_warning "Geyser plugin not found - Java Edition only"
fi
TESTS_TOTAL=$((TESTS_TOTAL + 1))

# 🎯 Test Load Handling
log_test "Testing load handling..."

# Simulate multiple queue joins/leaves
for i in {1..5}; do
    execute_command "sw join solo"
    execute_command "sw leave"
done

# 📋 Generate Test Report
echo ""
echo "🧪 Test Results Summary"
echo "======================="
echo "Total Tests: $TESTS_TOTAL"
echo "Passed: $TESTS_PASSED"
echo "Failed: $TESTS_FAILED"

if [[ $TESTS_FAILED -eq 0 ]]; then
    log_success "All tests passed! 🎉"
    echo ""
    echo "✅ SkyWars system is fully functional and ready for players!"
    echo ""
    echo "🎮 Key Features Verified:"
    echo "   • Configuration files are properly set up"
    echo "   • Database connectivity is working"
    echo "   • Queue system is operational"
    echo "   • Arena management is functional"
    echo "   • Statistics system is active"
    echo "   • Admin tools are available"
    echo "   • UI elements are in place"
    echo "   • Performance is acceptable"
    echo ""
    echo "🚀 Your professional SkyWars system is ready!"
    
    # Create success marker file
    echo "$(date): All SkyWars tests passed successfully" > "skywars-test-success.log"
    
    exit 0
else
    log_error "Some tests failed!"
    echo ""
    echo "❌ Issues found that need attention:"
    echo "   • $TESTS_FAILED out of $TESTS_TOTAL tests failed"
    echo "   • Check the error messages above for details"
    echo "   • Review configuration files and server logs"
    echo "   • Ensure all plugins are properly installed"
    echo ""
    echo "🔧 Recommended actions:"
    echo "   1. Check server logs: docker-compose logs mc"
    echo "   2. Verify plugin installations"
    echo "   3. Review configuration files for syntax errors"
    echo "   4. Ensure database is properly set up"
    echo "   5. Re-run this test after fixes"
    
    # Create failure marker file
    echo "$(date): SkyWars tests failed - $TESTS_FAILED/$TESTS_TOTAL" > "skywars-test-failure.log"
    
    exit 1
fi
