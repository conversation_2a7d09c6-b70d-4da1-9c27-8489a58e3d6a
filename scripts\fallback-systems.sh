#!/bin/bash

# 🛡️ SkyWars Fallback Systems
# Backup systems for when primary features fail
# Version: 2.1 - Enhanced Fallback Protection

set -e

echo "🛡️ Setting up SkyWars Fallback Systems..."
echo "========================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_fallback() { echo -e "${PURPLE}🛡️ $1${NC}"; }

# Create fallback configuration system
create_fallback_configs() {
    log_fallback "Creating fallback configuration system..."
    
    mkdir -p config/fallback
    
    # Create minimal working SkyWars config
    cat > config/fallback/minimal-skywars-config.yml << 'EOF'
# Minimal SkyWars Configuration - Fallback
# This config is used when the main configuration fails

# Basic settings only
locale: en
debug: false

# Simplified game settings
games:
  time-before-start: 30
  max-time: 600
  min-players: 2
  max-players: 8

# Basic arena settings
arenas:
  enable-arena-restoration: true
  restoration-delay: 10

# Minimal economy
economy:
  enabled: false

# Basic player settings
players:
  clear-inventory: true
  starting-items:
    enabled: true
    items:
      - "STONE_SWORD:1"
      - "BREAD:3"

# World settings
worlds:
  lobby-world: lobby
  return-to-lobby: true

# Simple messages
messages:
  prefix: "&8[&bSkyWars&8] "
  game-start: "&aGame starting!"
  game-end: "&cGame ended!"
EOF

    # Create fallback arena configuration
    cat > config/fallback/minimal-arena-config.yml << 'EOF'
# Minimal Arena Configuration - Fallback
arenas:
  fallback_arena:
    world: lobby
    enabled: true
    min-players: 2
    max-players: 8
    time-limit: 600
    grace-period: 30
EOF

    # Create fallback database configuration
    cat > config/fallback/fallback-database.sql << 'EOF'
-- Fallback Database Schema
-- Minimal tables for basic functionality

CREATE TABLE IF NOT EXISTS `skywars_basic_stats` (
    `uuid` VARCHAR(36) PRIMARY KEY,
    `username` VARCHAR(255) NOT NULL,
    `wins` INT DEFAULT 0,
    `losses` INT DEFAULT 0,
    `kills` INT DEFAULT 0,
    `deaths` INT DEFAULT 0,
    `last_played` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
EOF

    log_success "Fallback configurations created"
}

# Create fallback command system
create_fallback_commands() {
    log_fallback "Creating fallback command system..."
    
    cat > scripts/fallback-commands.sh << 'EOF'
#!/bin/bash
# Fallback Command System
# Basic commands when main system fails

handle_fallback_command() {
    local player="$1"
    local command="$2"
    
    case "$command" in
        "join")
            # Basic join functionality
            docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player 0 70 0"
            docker exec minecraft-server-docker-mc-1 rcon-cli "gamemode survival $player"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Joined basic SkyWars mode\",\"color\":\"green\"}"
            ;;
        "leave")
            # Basic leave functionality
            docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player 0 70 0"
            docker exec minecraft-server-docker-mc-1 rcon-cli "gamemode adventure $player"
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Left SkyWars\",\"color\":\"yellow\"}"
            ;;
        "stats")
            # Basic stats display
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Stats temporarily unavailable\",\"color\":\"red\"}"
            ;;
        *)
            docker exec minecraft-server-docker-mc-1 rcon-cli "tellraw $player {\"text\":\"Available: join, leave, stats\",\"color\":\"gray\"}"
            ;;
    esac
}

# Export function
export -f handle_fallback_command
EOF

    chmod +x scripts/fallback-commands.sh
    log_success "Fallback command system created"
}

# Create database fallback system
create_database_fallback() {
    log_fallback "Creating database fallback system..."
    
    cat > scripts/database-fallback.sh << 'EOF'
#!/bin/bash
# Database Fallback System

# Function to check database health
check_database_health() {
    if docker exec minecraft-server-docker-db-1 mysqladmin ping -h localhost -u hamza -p"Hh@#2021" --silent 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to use file-based storage as fallback
use_file_storage() {
    local player="$1"
    local action="$2"
    local data="$3"
    
    local storage_dir="data/fallback-storage"
    mkdir -p "$storage_dir"
    
    case "$action" in
        "save_stats")
            echo "$data" > "$storage_dir/${player}_stats.txt"
            ;;
        "load_stats")
            if [[ -f "$storage_dir/${player}_stats.txt" ]]; then
                cat "$storage_dir/${player}_stats.txt"
            else
                echo "wins:0,losses:0,kills:0,deaths:0"
            fi
            ;;
        "save_game")
            echo "$(date):$data" >> "$storage_dir/${player}_games.log"
            ;;
    esac
}

# Function to attempt database recovery
attempt_database_recovery() {
    echo "Attempting database recovery..."
    
    # Restart database container
    docker-compose restart db
    
    # Wait for recovery
    for i in {1..30}; do
        if check_database_health; then
            echo "Database recovered successfully"
            return 0
        fi
        sleep 2
    done
    
    echo "Database recovery failed, using file storage"
    return 1
}

# Main fallback handler
handle_database_fallback() {
    if ! check_database_health; then
        echo "Database unavailable, attempting recovery..."
        
        if ! attempt_database_recovery; then
            echo "Using file-based storage as fallback"
            export USE_FILE_STORAGE=true
        fi
    fi
}

# Export functions
export -f check_database_health
export -f use_file_storage
export -f attempt_database_recovery
export -f handle_database_fallback
EOF

    chmod +x scripts/database-fallback.sh
    log_success "Database fallback system created"
}

# Create plugin fallback system
create_plugin_fallback() {
    log_fallback "Creating plugin fallback system..."
    
    cat > scripts/plugin-fallback.sh << 'EOF'
#!/bin/bash
# Plugin Fallback System

# Function to check if plugin is loaded
check_plugin_loaded() {
    local plugin_name="$1"
    local plugins=$(docker exec minecraft-server-docker-mc-1 rcon-cli "plugins" 2>/dev/null || echo "")
    
    if echo "$plugins" | grep -q "$plugin_name"; then
        return 0
    else
        return 1
    fi
}

# Function to handle AuthMe fallback
handle_authme_fallback() {
    if ! check_plugin_loaded "AuthMe"; then
        echo "AuthMe not available, using basic authentication"
        
        # Create basic auth function
        basic_auth_check() {
            local player="$1"
            # Always return true for fallback mode
            return 0
        }
        
        export -f basic_auth_check
    fi
}

# Function to handle Essentials fallback
handle_essentials_fallback() {
    if ! check_plugin_loaded "Essentials"; then
        echo "Essentials not available, using basic commands"
        
        # Create basic teleport function
        basic_teleport() {
            local player="$1"
            local x="${2:-0}"
            local y="${3:-70}"
            local z="${4:-0}"
            
            docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player $x $y $z"
        }
        
        export -f basic_teleport
    fi
}

# Function to handle Multiverse fallback
handle_multiverse_fallback() {
    if ! check_plugin_loaded "Multiverse-Core"; then
        echo "Multiverse not available, using basic world management"
        
        # Create basic world teleport function
        basic_world_tp() {
            local player="$1"
            local world="$2"
            
            # Use basic tp command
            docker exec minecraft-server-docker-mc-1 rcon-cli "tp $player 0 70 0"
        }
        
        export -f basic_world_tp
    fi
}

# Main plugin fallback handler
handle_plugin_fallbacks() {
    echo "Checking plugin availability and setting up fallbacks..."
    
    handle_authme_fallback
    handle_essentials_fallback
    handle_multiverse_fallback
    
    echo "Plugin fallback systems ready"
}

# Export main function
export -f handle_plugin_fallbacks
EOF

    chmod +x scripts/plugin-fallback.sh
    log_success "Plugin fallback system created"
}

# Create server fallback system
create_server_fallback() {
    log_fallback "Creating server fallback system..."
    
    cat > scripts/server-fallback.sh << 'EOF'
#!/bin/bash
# Server Fallback System

# Function to check server health
check_server_health() {
    if docker ps | grep -q minecraft-server-docker-mc-1; then
        # Check if server is responsive
        if docker exec minecraft-server-docker-mc-1 rcon-cli "list" >/dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# Function to start server in safe mode
start_safe_mode() {
    echo "Starting server in safe mode..."
    
    # Create safe mode configuration
    cat > safe-mode-server.properties << 'EOF'
# Safe Mode Server Configuration
online-mode=false
difficulty=peaceful
gamemode=adventure
max-players=10
view-distance=6
spawn-protection=16
enable-command-block=false
EOF

    # Backup current server.properties
    if [[ -f "files/server.properties" ]]; then
        cp files/server.properties files/server.properties.backup
    fi
    
    # Use safe mode configuration
    cp safe-mode-server.properties files/server.properties
    
    # Restart server
    docker-compose restart mc
    
    echo "Server started in safe mode"
}

# Function to handle server fallback
handle_server_fallback() {
    if ! check_server_health; then
        echo "Server health check failed, attempting recovery..."
        
        # Try normal restart first
        docker-compose restart mc
        
        # Wait for restart
        sleep 30
        
        if ! check_server_health; then
            echo "Normal restart failed, starting in safe mode..."
            start_safe_mode
        else
            echo "Server recovered successfully"
        fi
    fi
}

# Export functions
export -f check_server_health
export -f start_safe_mode
export -f handle_server_fallback
EOF

    chmod +x scripts/server-fallback.sh
    log_success "Server fallback system created"
}

# Create master fallback controller
create_master_fallback() {
    log_fallback "Creating master fallback controller..."
    
    cat > scripts/master-fallback.sh << 'EOF'
#!/bin/bash
# Master Fallback Controller
# Coordinates all fallback systems

# Source all fallback systems
source scripts/database-fallback.sh 2>/dev/null || true
source scripts/plugin-fallback.sh 2>/dev/null || true
source scripts/server-fallback.sh 2>/dev/null || true
source scripts/fallback-commands.sh 2>/dev/null || true

# Function to activate all fallback systems
activate_fallback_mode() {
    echo "🛡️ Activating SkyWars Fallback Mode..."
    echo "====================================="
    
    # Check and handle server issues
    handle_server_fallback
    
    # Check and handle database issues
    handle_database_fallback
    
    # Check and handle plugin issues
    handle_plugin_fallbacks
    
    # Set fallback mode flag
    export SKYWARS_FALLBACK_MODE=true
    echo "SKYWARS_FALLBACK_MODE=true" > .env.fallback
    
    echo "✅ Fallback mode activated"
    echo ""
    echo "📋 Fallback Features Active:"
    echo "   • Basic SkyWars commands"
    echo "   • File-based data storage"
    echo "   • Simplified game mechanics"
    echo "   • Safe mode server operation"
    echo ""
    echo "⚠️  Some advanced features may be unavailable"
}

# Function to deactivate fallback mode
deactivate_fallback_mode() {
    echo "🔄 Deactivating SkyWars Fallback Mode..."
    echo "======================================="
    
    # Remove fallback mode flag
    unset SKYWARS_FALLBACK_MODE
    rm -f .env.fallback
    
    # Restore normal configurations
    if [[ -f "files/server.properties.backup" ]]; then
        cp files/server.properties.backup files/server.properties
        docker-compose restart mc
    fi
    
    echo "✅ Fallback mode deactivated"
    echo "🔄 System restored to normal operation"
}

# Function to check if fallback mode is needed
check_fallback_needed() {
    local issues=0
    
    # Check server health
    if ! check_server_health 2>/dev/null; then
        echo "⚠️  Server health issues detected"
        ((issues++))
    fi
    
    # Check database health
    if ! check_database_health 2>/dev/null; then
        echo "⚠️  Database health issues detected"
        ((issues++))
    fi
    
    # Check critical plugins
    local critical_plugins=("AuthMe" "Essentials")
    for plugin in "${critical_plugins[@]}"; do
        if ! check_plugin_loaded "$plugin" 2>/dev/null; then
            echo "⚠️  Critical plugin missing: $plugin"
            ((issues++))
        fi
    done
    
    if [[ $issues -gt 0 ]]; then
        echo "🛡️ $issues issue(s) detected - fallback mode recommended"
        return 0
    else
        echo "✅ All systems healthy - fallback mode not needed"
        return 1
    fi
}

# Main fallback handler
main() {
    local action="${1:-check}"
    
    case "$action" in
        "activate")
            activate_fallback_mode
            ;;
        "deactivate")
            deactivate_fallback_mode
            ;;
        "check")
            check_fallback_needed
            ;;
        "auto")
            if check_fallback_needed; then
                activate_fallback_mode
            fi
            ;;
        *)
            echo "Usage: $0 {activate|deactivate|check|auto}"
            echo ""
            echo "Commands:"
            echo "  activate   - Force activate fallback mode"
            echo "  deactivate - Deactivate fallback mode"
            echo "  check      - Check if fallback mode is needed"
            echo "  auto       - Automatically activate if needed"
            ;;
    esac
}

# Export main function
export -f activate_fallback_mode
export -f deactivate_fallback_mode
export -f check_fallback_needed

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
EOF

    chmod +x scripts/master-fallback.sh
    log_success "Master fallback controller created"
}

# Main execution
main() {
    log_info "Setting up comprehensive fallback systems..."
    
    # Create all fallback systems
    create_fallback_configs
    create_fallback_commands
    create_database_fallback
    create_plugin_fallback
    create_server_fallback
    create_master_fallback
    
    # Test fallback systems
    log_info "Testing fallback systems..."
    if ./scripts/master-fallback.sh check; then
        log_success "System is healthy - fallback systems ready if needed"
    else
        log_warning "Issues detected - fallback systems may be activated"
    fi
    
    log_success "🛡️ Fallback systems setup complete!"
    echo ""
    echo "✅ Fallback Features:"
    echo "   • Configuration fallbacks"
    echo "   • Command system fallbacks"
    echo "   • Database fallbacks (file storage)"
    echo "   • Plugin fallbacks"
    echo "   • Server safe mode"
    echo "   • Master fallback controller"
    echo ""
    echo "🔧 Available Tools:"
    echo "   • ./scripts/master-fallback.sh check - Check system health"
    echo "   • ./scripts/master-fallback.sh auto - Auto-activate if needed"
    echo "   • ./scripts/master-fallback.sh activate - Force activate"
    echo "   • ./scripts/master-fallback.sh deactivate - Deactivate"
    echo ""
    echo "📋 Fallback Protection:"
    echo "   • Automatic detection of system issues"
    echo "   • Graceful degradation of features"
    echo "   • Minimal functionality preservation"
    echo "   • Easy recovery to normal operation"
    echo ""
    log_success "Fallback systems are ready! 🛡️"
}

# Run main function
main "$@"
